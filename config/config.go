package config

import (
	"time"
	"unsafe"

	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/xconf/remote"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/atomic"
)

var conf atomic.Pointer // *Config

type IpConfig struct {
	DirPath  string `xconf:"dir_path"`
	City     bool   `xconf:"city"`
	Language string `xconf:"language"`
}

func Get() *Config { return (*Config)(conf.Load()) }

type Config struct {
	Host      string `xconf:"host"`
	AdminHost string `xconf:"admin_host"`
	Register  bool   `xconf:"register"`

	ClientID     string `xconf:"client_id"`
	ClientSecret string `xconf:"client_secret"`
	ClientVerify string `xconf:"client_verify"`

	LogWebhook string `xconf:"log_webhook"`
	LogPrefix  string `xconf:"log_prefix"`

	RedeemFeed string `xconf:"redeem_feed"`

	IpTrust int `xconf:"ip_trust"`

	AwardInfoTTL        time.Duration `xconf:"award_info_ttl"`
	CmsSurveyTTL        time.Duration `xconf:"cms_survey_ttl"`
	PreAwardTemplateTTL time.Duration `xconf:"pre_award_template_ttl"`
	ClientConfigTTL     time.Duration `xconf:"client_config_ttl"`

	Survey struct {
		Name        string        `xconf:"name"`
		Sharding    int           `xconf:"sharding"`
		Parallel    int           `xconf:"parallel"`
		Worker      int           `xconf:"worker"`
		Batch       int           `xconf:"batch"`
		Resting     time.Duration `xconf:"resting"`
		Barrier     time.Duration `xconf:"barrier"`
		Immediately bool          `xconf:"immediately"`
	} `xconf:"survey"`

	Award struct {
		Name        string        `xconf:"name"`
		Sharding    int           `xconf:"sharding"`
		Parallel    int           `xconf:"parallel"`
		Worker      int           `xconf:"worker"`
		Batch       int           `xconf:"batch"`
		Resting     time.Duration `xconf:"resting"`
		Barrier     time.Duration `xconf:"barrier"`
		Immediately bool          `xconf:"immediately"`
		Strategy    struct {
			Delay    time.Duration `xconf:"delay"`
			MaxDelay time.Duration `xconf:"max_delay"`
		} `xconf:"strategy"`
	} `xconf:"award"`

	RealTimeEvent struct {
		HiddenKey       bool    `xconf:"hiddenKey"`
		Key             string  `xconf:"key"`
		HiddenPeriodKey bool    `xconf:"hiddenPeriodKey"`
		PeriodKey       string  `xconf:"periodKey"`
		Host            string  `xconf:"host"`
		UpLog           string  `xconf:"upLog"`
		InClientIds     []int64 `xconf:"inClientIds"`
	}

	Jwt struct {
		Auth struct {
			Alg    string `xconf:"alg"`
			Secret string `xconf:"secret"`
		} `xconf:"auth"`
	} `xconf:"jwt"`

	Keys    map[string]string `xconf:"keys"`
	Sparrow struct {
		Broker struct {
			Kafka struct {
				Writer struct {
					Brokers []string `xconf:"Brokers"`
				} `xconf:"writer"`
			} `xconf:"kafka"`
		} `xconf:"broker"`
	} `xconf:"sparrow"`
	Push struct {
		StrategyInvoke string `xconf:"strategy_invoke"`
	} `xconf:"push"`

	IpLocal IpConfig `xconf:"ip_local"`
}

func Startup() error {
	xconf.RegisterReload(Reload)
	return Reload()
}

func Reload() error {
	// default config
	c := &Config{
		Host:     "",
		Register: false,
		IpLocal: IpConfig{
			DirPath:  "",
			City:     false,
			Language: "en",
		},
	}

	err := xconf.Unmarshal(c)
	if err != nil {
		return err
	}

	conf.Store(unsafe.Pointer(c))
	return nil
}
