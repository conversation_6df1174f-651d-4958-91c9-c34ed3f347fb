# Generated with protoc-gen-openapi
# https://gitlab.papegames.com/fringe/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
servers:
    - url: https://survey-graph.papegames.com
paths:
    /v1/survey/answer:
        get:
            tags:
                - SurveyService
            description: |-
                获取问卷答题信息
                 角色或者账号登录需验证authority
            operationId: Answer
            parameters:
                - name: survey_id
                  in: query
                  description: 问卷ID
                  required: true
                  schema:
                    type: string
                - name: openid
                  in: query
                  description: 用户ID
                  schema:
                    type: string
                - name: role_id
                  in: query
                  description: 角色ID
                  schema:
                    type: string
                - name: device_id
                  in: query
                  description: 设备ID
                  schema:
                    type: string
                - name: lang
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/AnswerResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/config:
        get:
            tags:
                - SurveyService
            description: 获取配置
            operationId: Config
            parameters:
                - name: survey_id
                  in: query
                  description: 问卷ID
                  required: true
                  schema:
                    type: string
                - name: lang
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/ConfigResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/group/config:
        get:
            tags:
                - SurveyService
            description: 获取问卷组配置
            operationId: GroupConfig
            parameters:
                - name: group
                  in: query
                  description: 问卷ID
                  required: true
                  schema:
                    type: string
                - name: lang
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/GroupConfigResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/health:
        get:
            tags:
                - SurveyService
            description: 健康检测
            operationId: Health
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/internal/config/reload:
        post:
            tags:
                - SurveyServiceAdmin
            description: 配置重载
            operationId: ConfigReload
            parameters:
                - name: survey_id
                  in: query
                  description: 问卷ID
                  schema:
                    type: string
                - name: group
                  in: query
                  description: 问卷组ID
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/signin:
        post:
            tags:
                - SurveyService
            description: 登陆
            operationId: Signin
            parameters:
                - name: token
                  in: query
                  description: 用户Token
                  schema:
                    type: string
                - name: survey_id
                  in: query
                  description: 问卷ID
                  required: true
                  schema:
                    type: string
                - name: openid
                  in: query
                  description: 用户ID
                  schema:
                    type: string
                - name: role_id
                  in: query
                  description: 角色ID
                  schema:
                    type: string
                - name: device_id
                  in: query
                  description: 设备ID
                  schema:
                    type: string
                - name: timezone
                  in: query
                  description: 时区
                  schema:
                    type: string
                - name: group
                  in: query
                  description: 问卷组
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SigninResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/submit:
        post:
            tags:
                - SurveyService
            description: |-
                问卷提交
                 角色或者账号登录需验证authority
            operationId: SubmitSurvey
            parameters:
                - name: survey_id
                  in: query
                  description: 问卷ID
                  required: true
                  schema:
                    type: string
                - name: timezone
                  in: query
                  description: 时区
                  schema:
                    type: string
                - name: group
                  in: query
                  description: 问卷组
                  schema:
                    type: string
                - name: platid
                  in: query
                  description: 渠道id
                  schema:
                    type: string
            requestBody:
                content:
                    application/octet-stream:
                        schema:
                            type: bytes
                            description: "问卷内容(经snappy压缩),eg. \n  {\n  \t\"survey_id\": \"xxxxx\",\n    \"openid\" : \"\",\n    \"role_id\" : \"\",\n    \"device_id\" : \"\",\n  \t\"begin_time\": \"2022-01-02T09:13:47+08:00\",\n  \t\"end_time\": \"2022-01-02T09:13:47+08:00\",\n  \t\"survey_records\": {\n  \t\t\"hello world\": [{\n  \t\t\t\"option\": \"A\",\n  \t\t\t\"text\": \"yes\"\n  \t\t}]\n  \t},\n  \t\"extra\": \"xxxx\",\n    \"flag\": 1\n  }"
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SubmitSurveyResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
components:
    schemas:
        AnswerResponse:
            type: object
            properties:
                survey_records:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: "问卷答题信息 {\t\t\"hello world\": [{\t\t\t\"option\": \"A1\",\t\t\t\"text\": \"yes\"\t\t}, {\t\t\t\"option\": \"B1\",\t\t\t\"text\": \"no\"\t\t}],\t\t\"hello world2\": [{\t\t\t\"option\": \"A2\",\t\t\t\"text\": \"1\"\t\t}, {\t\t\t\"option\": \"B2\",\t\t\t\"text\": \"2\"\t\t}] }"
                schema:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: schema配置
                materials_config:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: materials config
                web_settings:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: web_settings
                name:
                    type: string
            description: Response message for SurveyService.Answer
        AwardInfo:
            type: object
            properties:
                type:
                    type: string
                    description: '形式, 0: 邮件 1: 兑换码'
                value:
                    type: string
                    description: 值,形式为兑换码时表示兑换码
                survey_record_id:
                    type: integer
                    description: 问卷记录ID
                    format: int32
            description: 发奖信息
        ConfigResponse:
            type: object
            properties:
                schema:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: schema配置
                materials_config:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: materials config
                web_settings:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: web_settings
                name:
                    type: string
                    description: name
                font:
                    $ref: '#/components/schemas/RawMessage'
            description: Response message for SurveyService.Config
        GroupConfigResponse:
            type: object
            properties:
                group:
                    type: string
                    description: 问卷组
                survey_id:
                    type: string
                    description: 问卷id
        RawMessage:
            type: object
        SigninResponse:
            type: object
            properties:
                authority:
                    type: string
                    description: The authority, x-Authority
                award_info:
                    allOf:
                        - $ref: '#/components/schemas/AwardInfo'
                    description: 发奖信息 错误码1004时有值
                sign:
                    type: string
                    description: 签名字段 错误码1004时有值
            description: Response message for SurveyService.Signin
        SubmitSurveyResponse:
            type: object
            properties:
                award_info:
                    allOf:
                        - $ref: '#/components/schemas/AwardInfo'
                    description: 发奖信息
                sign:
                    type: string
                    description: 签名字段
            description: Response message for SurveyService.SubmitSurvey
        _failReturn:
            type: object
            properties:
                code:
                    example: 400
                    type: integer
                    format: int32
                info:
                    example: error
                    type: string
                request_id:
                    example: 16vHbfABAd
                    type: string
tags:
    - name: SurveyService
      description: 问卷
    - name: SurveyServiceAdmin
      description: 管理端
