package control

import (
	"context"

	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/model"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/service"
	"gitlab.papegames.com/fringe/survey/service/survey"
	"gitlab.papegames.com/fringe/survey/shared"
	"gitlab.papegames.com/fringe/survey/shared/geoip"
	"gitlab.papegames.com/fringe/survey/watch"
)

var adminIns = new(AdminControl)

type AdminControl struct {
	proto.UnimplementedSurveyServiceAdminServer
}

func GetAdmin() *AdminControl {
	return adminIns
}

func (AdminControl) ConfigReload(ctx context.Context, request *proto.ConfigReloadRequest) (*xtype.Empty, error) {
	shared.MetricSurveyAllReqTotal.Inc("", "", request.SurveyId, "", "config/reload")
	if request.SurveyId != "" {
		err := watch.ExpireSurveyConfig(request.SurveyId)
		logger := xlog.FromContext(ctx)
		if err != nil {
			logger.Error("watch.ExpireSurveyConfig failed", xlog.String("survey_id", request.SurveyId), xlog.Err(err))
			return nil, err
		}
	}
	if request.Group != "" {
		err := watch.ExpireSurveyGroupConfig(request.Group)
		logger := xlog.FromContext(ctx)
		if err != nil {
			logger.Error("watch.ExpireGroupConfig failed", xlog.String("group_hash_code", request.Group), xlog.Err(err))
			return nil, err
		}
	}
	return nil, nil
}

func (Control) Config(ctx context.Context, request *proto.ConfigRequest) (*proto.ConfigResponse, error) {
	logger := xlog.FromContext(ctx)
	cmsSurvey, err := model.GetCmsSurvey(request.SurveyId)
	if err != nil {
		logger.Error("model.GetCmsSurvey failed", xlog.String("survey_id", request.SurveyId), xlog.Err(err))
		return nil, err
	}

	shared.MetricSurveyAllReqTotal.Inc(cmsSurvey.Clientid,
		xcast.ToString(cmsSurvey.SurveyId), cmsSurvey.HashCode, cmsSurvey.Name, "config")

	var schema []byte
	var ok bool
	ms := cmsSurvey.GetMultilingualSchemaDecMap()
	if ms == nil {
		schema = cmsSurvey.GetSchemaDec()
		if len(schema) != 0 {
			ok = true
		}
	} else {
		lang := request.Lang
		if lang == "" {
			schema, ok = ms[cmsSurvey.GetDefaultLanguage()]
		} else {
			// 如果没有对应的语言，就使用默认语言
			schema, ok = ms[lang]
			if !ok {
				schema, ok = ms[cmsSurvey.GetDefaultLanguage()]
			}
		}
	}

	if !ok {
		logger.Error("cmsSurvey.GetSchema failed", xlog.String("survey_id", request.SurveyId), xlog.Err(err))
		return nil, shared.EcodeSurveyConfigInvalid
	}

	return &proto.ConfigResponse{
		Name:            cmsSurvey.Name,
		Schema:          xtype.NewRawMessage(schema),
		MaterialsConfig: cmsSurvey.SettingStruct.MaterialsConfig,
		WebSettings:     xtype.NewRawMessage([]byte(cmsSurvey.WebSettings)),
		Font:            xtype.NewRawMessage(cmsSurvey.Font),
	}, nil
}

func (Control) GroupConfig(ctx context.Context,
	request *proto.GroupConfigRequest) (*proto.GroupConfigResponse, error) {
	logger := xlog.FromContext(ctx)
	groupVersion, err := survey.CheckSurveyGroupStatus(ctx, request.GetGroup(), "config")
	if err != nil {
		logger.Error("model.GetCmsSurveyGroup failed",
			xlog.String("group_hash_code", request.GetGroup()), xlog.Err(err))
		return nil, err
	}
	if groupVersion == nil || groupVersion.Settings == nil {
		logger.Error("group settings error", xlog.String("group_hash_code", request.GetGroup()))
		return nil, shared.EcodeSurveyGroupNotExisted
	}
	shared.MetricSurveyAllReqTotal.Inc(groupVersion.Clientid,
		xcast.ToString(groupVersion.GroupId), groupVersion.HashCode, groupVersion.Name, "config")
	var surveyId string
	switch groupVersion.Type {
	case proto.CmsSurveyGroupVersion_Lang:
		if groupVersion.Settings.GroupSurvey[request.Lang] != nil {
			surveyId = groupVersion.Settings.GroupSurvey[request.Lang].HashCode
		}
	case proto.CmsSurveyGroupVersion_Location:
		ip := xnet.SourceIP(xgin.FromContext(ctx).Request, config.Get().IpTrust)
		ipLocal, err := geoip.GetIpLocal(ip)
		if err != nil {
			//非致命错误
			logger.Info("config.GetIpLocal failed", xlog.String("ip", ip), xlog.Err(err))
		}
		logger.Info("local ip", xlog.String("ip", ip), xlog.Any("ip_local", ipLocal), xlog.String("xff", xgin.FromContext(ctx).Request.Header.Get(xnet.Forwarded)))
		if ipLocal != nil && ipLocal.CountryCode != "" {
			if groupVersion.Settings.GroupSurvey[ipLocal.CountryCode] != nil {
				surveyId = groupVersion.Settings.GroupSurvey[ipLocal.CountryCode].HashCode
			}
		}
	}
	if surveyId == "" {
		surveyId, err = service.GetGroupDefalutSurvey(groupVersion.Settings.GroupSurvey)
		if err != nil {
			logger.Error("getGroupDefalutSurvey failed", xlog.String("group_hash_code", request.GetGroup()), xlog.Err(err))
			return nil, err
		}
	}
	return &proto.GroupConfigResponse{
		Group:    request.Group,
		SurveyId: surveyId,
	}, nil
}
