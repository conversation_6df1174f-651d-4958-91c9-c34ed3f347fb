package control

import (
	"context"

	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/service/survey"
)

func (Control) SubmitSurvey(ctx context.Context, request *proto.SubmitSurveyRequest) (*proto.SubmitSurveyResponse, error) {
	logger := xlog.FromContext(ctx)
	creq := xgin.FromContext(ctx).Request
	ip := xnet.SourceIP(creq, config.Get().IpTrust)
	authority := creq.Header.Get(xnet.Authority)
	res, err := survey.Submit(ctx, request, ip, authority)
	if err != nil {
		logger.Error("survey.Submit failed", xlog.Any("request_body", request),
			xlog.String("ip", ip), xlog.String("authority", authority), xlog.Err(err))
	}
	return res, err
}
