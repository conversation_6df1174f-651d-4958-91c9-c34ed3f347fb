package control

import (
	"context"

	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/service/survey"
)

func (Control) Signin(ctx context.Context, request *proto.SigninRequest) (*proto.SigninResponse, error) {
	logger := xlog.FromContext(ctx)
	ip := xnet.SourceIP(xgin.FromContext(ctx).Request, config.Get().IpTrust)
	res, err := survey.Signin(ctx, request, ip)
	if err != nil {
		logger.Error("survey.Signin faild", xlog.Any("request_body", request), xlog.String("ip", ip), xlog.Err(err))
	}
	return res, err
}
