package control

import (
	"context"
	"encoding/json"

	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/model"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/service/survey"
	"gitlab.papegames.com/fringe/survey/shared"
)

func (Control) Answer(ctx context.Context, request *proto.AnswerRequest) (*proto.AnswerResponse, error) {
	logger := xlog.FromContext(ctx)
	cmsSurvey, err := model.GetCmsSurvey(request.SurveyId)
	if err != nil {
		logger.Error("model.GetCmsSurvey failed", xlog.String("survey_id", request.SurveyId), xlog.Err(err))
		return nil, err
	}
	shared.MetricSurveyAllReqTotal.Inc(cmsSurvey.Clientid,
		xcast.ToString(cmsSurvey.SurveyId), cmsSurvey.HashCode, cmsSurvey.Name, "answer")

	c := xgin.FromContext(ctx)
	authority := c.Request.Header.Get(xnet.Authority)
	ip := xnet.SourceIP(xgin.FromContext(ctx).Request, config.Get().IpTrust)
	surveyInfo, err := survey.GetSurveyInfo(ctx, request, cmsSurvey, ip, authority)
	if err != nil {
		logger.Error("survey.GetSurveyInfo failed", xlog.String("survey_id", request.SurveyId), xlog.Err(err))
		return nil, err
	}

	surveyRecordsRaw, err := json.Marshal(surveyInfo.SurveyRecords)
	if err != nil {
		logger.Error("json.Marshal failed", xlog.String("survey_id", request.SurveyId),
			xlog.String("openid", request.Openid), xlog.String("roleid", request.RoleId), xlog.Err(err))
		return nil, err
	}

	var schema []byte
	var ok bool
	ms := cmsSurvey.GetMultilingualSchemaDecMap()
	if ms == nil {
		schema = cmsSurvey.GetSchemaDec()
		if len(schema) != 0 {
			ok = true
		}
	} else {
		lang := request.Lang
		if lang == "" {
			lang = cmsSurvey.GetDefaultLanguage()
		}
		schema, ok = ms[lang]
	}

	if !ok {
		logger.Error("model.GetSchema failed", xlog.String("survey_id", request.SurveyId), xlog.Err(err))
		return nil, shared.EcodeSurveyConfigInvalid
	}

	return &proto.AnswerResponse{
		Name:            cmsSurvey.Name,
		SurveyRecords:   xtype.NewRawMessage(surveyRecordsRaw),
		Schema:          xtype.NewRawMessage(schema),
		MaterialsConfig: cmsSurvey.SettingStruct.MaterialsConfig,
		WebSettings:     xtype.NewRawMessage([]byte(cmsSurvey.WebSettings)),
	}, nil

}
