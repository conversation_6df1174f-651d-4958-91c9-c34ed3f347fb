package service

import (
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/shared"
)

func GetGroupDefalutSurvey(groupConfig map[string]*proto.GroupSurvey) (string, error) {
	var surveyId string
	for _, v := range groupConfig {
		if v.<PERSON><PERSON><PERSON> {
			surveyId = v.HashCode
		}
	}
	if surveyId == "" {
		return "", shared.EcodeSurveyGroupWrongSetting
	}
	return surveyId, nil
}
