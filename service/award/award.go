package award

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/retry"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdefer"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xhttp"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/retryable"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/model"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/shared"
	"gitlab.papegames.com/fringe/survey/shared/push"
	"go.uber.org/zap"
)

const maxRetries = 32 // 最大重试次数

var redeemDialer = xhttp.NewClient()

var awarder *Awarder

func Startup() error {
	conf := config.Get()

	retryStrategy := &retry.Exponential{
		Delay:      conf.Award.Strategy.Delay,
		Multiplier: 1.6,
		Jitter:     0.2,
		MaxDelay:   conf.Award.Strategy.MaxDelay,
	}

	awarder = new(Awarder)
	awarder.do = retryable.New(
		retryable.WithImmediately(conf.Award.Immediately),
		retryable.WithName(conf.Award.Name),
		retryable.WithSharding(conf.Award.Sharding),
		retryable.WithBatch(conf.Award.Batch),
		retryable.WithResting(conf.Award.Resting),
		retryable.WithBarrier(conf.Award.Barrier),
		retryable.WithParallel(conf.Award.Parallel),
		retryable.WithCodec(new(codec)),

		retryable.WithStorage(retryable.NewStorage("redis", database.GetRedis())),
		retryable.WithBackoff(
			func(r *retryable.Record) time.Duration {
				return retryStrategy.Backoff(r.Retries)
			},
		),
	)

	awarder.do.Do = awarder.Retry
	xdefer.Register(awarder.do.Close)
	return awarder.do.Start()
}

type Awarder struct {
	do *retryable.Retryable
}

// 发奖重试
func (a *Awarder) Retry(r *retryable.Record) error {
	// logger := xlog.GetEntry().SetPrefix("Awarder(" + r.Key + ")")
	logger := xlog.S().With(zap.String("AwarderKey", r.Key))
	// defer xlog.PutEntry(logger)

	if r.Retries > maxRetries {
		logger.Error("retryable: RawData(%s) Retries(%d) > maxRetries(%d)", r.Key, r.Retries, maxRetries)
		return retryable.ErrShouldAbortRetry
	}

	data, ok := r.RawData.([]byte)
	if !ok {
		logger.Error("retryable: RawData(%s) Type must be []byte", r.Key)
		return retryable.ErrShouldAbortRetry
	}

	ids := strings.Split(string(data), "|")
	if len(ids) != 7 {
		logger.Error("retryable: RawData(%s) must be 7 fields", r.Key)
		return retryable.ErrShouldAbortRetry
	}

	surveyId := ids[0]
	openid := ids[1]
	roleId := ids[2]
	clientid := ids[3]
	awardField := ids[4]
	recordId := ids[5]
	awardType := ids[6]
	recordIdInt64, _ := strconv.ParseInt(recordId, 10, 64)
	recordIdInt32 := int32(recordIdInt64)

	switch awardType {
	case model.GiveOutTypeEmail:
		err := emailPigeon(context.Background(), openid, roleId, clientid, awardField)
		if err != nil {
			logger.Error("retryable: RawData(%s) emailPigeon error(%v)", r.Key, err)
			return err
		}
		if err := SetAwardInfo(context.Background(), surveyId, openid, roleId, &proto.AwardInfo{
			SurveyRecordId: recordIdInt32,
			Type:           awardType,
		}); err != nil {
			logger.Error("retryable: RawData(%s) SetAwardInfo error(%v)", r.Key, err)
		}
	case model.GiveOutTypeRedeem:
		redeem, err := redeemPigeon(clientid, awardField)
		if err != nil {
			logger.Error("retryable: RawData(%s) redeemPigeon error(%v)", r.Key, err)
			return err
		}
		if err := SetAwardInfo(context.Background(), surveyId, openid, roleId, &proto.AwardInfo{
			SurveyRecordId: recordIdInt32,
			Type:           awardType,
			Value:          redeem,
		}); err != nil {
			logger.Error("retryable: RawData(%s) SetAwardInfo error(%v)", r.Key, err)
		}
	case model.GiveOutTypePush:
		tmp := strings.Split(awardField, "@@")
		if len(tmp) != 3 {
			logger.Error("retryable: RawData(%s) PushAwardConfig error (%s)", r.Key, awardField)
			return retryable.ErrShouldAbortRetry
		}
		err := push.StrategyInvoke(context.Background(), &push.StrategyInvokeParam{
			SceneCode:   tmp[0],
			ChannelCode: tmp[1],
			StrategyID:  xcast.ToInt64(tmp[2]),
			Users: []*push.StrategyInvokeUser{
				&(push.StrategyInvokeUser{
					OpenID: xcast.ToInt64(openid),
					RoleID: xcast.ToInt64(roleId),
				}),
			},
			ClientID: xcast.ToInt64(clientid),
		})
		if err != nil {
			logger.Error("retryable: RawData(%s) emailPigeon error(%v)", r.Key, err)
			return err
		}
		if err := SetAwardInfo(context.Background(), surveyId, openid, roleId, &proto.AwardInfo{
			SurveyRecordId: recordIdInt32,
			Type:           awardType,
		}); err != nil {
			logger.Error("retryable: RawData(%s) SetAwardInfo error(%v)", r.Key, err)
		}
	default:
		logger.Error("retryable: RawData(%s) unknown giveout type(%s)", r.Key, ids[6])
		return retryable.ErrShouldAbortRetry
	}

	return nil
}

func (a *Awarder) Push(surveyId, openid, roleId, clientid, awardField string, recordId string, awardType string) error {
	return a.do.Push(surveyId+"|"+recordId, []byte(surveyId+"|"+openid+"|"+roleId+"|"+clientid+"|"+awardField+"|"+recordId+"|"+awardType))
}

func Email(ctx context.Context, surveyId, openid, roleId, clientid, templateId string, awardInfo *proto.AwardInfo) {
	awardInfo.Type = model.GiveOutTypeEmail
	var ret string
	err := emailPigeon(ctx, openid, roleId, clientid, templateId)
	if err != nil {
		ret = "false"
		xlog.FromContext(ctx).Error(fmt.Sprintf("emailPigeon(%v,%v) error(%v)", openid, roleId, err))
		// 放入重试队列
		if err := awarder.Push(surveyId, openid, roleId, clientid, templateId, strconv.FormatInt(int64(awardInfo.SurveyRecordId), 10), awardInfo.Type); err != nil {
			xlog.FromContext(ctx).Error(fmt.Sprintf("push(%v,%v,%v,%v,%v,%v) error(%v)", surveyId, openid, roleId, clientid, templateId, awardInfo, err))
		}
	} else {
		ret = "true"
		err := SetAwardInfo(ctx, surveyId, openid, roleId, awardInfo)
		if err != nil {
			xlog.FromContext(ctx).Error(fmt.Sprintf("SetAwardInfo(%v,%v,%v,%v) error(%v)", surveyId, openid, roleId, awardInfo, err))
		}
	}
	shared.MetricAward.Inc(clientid, model.GiveOutTypeEmail, ret)
}

func emailPigeon(ctx context.Context, openid, roleId, clientid, templateid string) error {
	pat, err := model.GetPreAwardTemplate(templateid)
	if err != nil {
		return fmt.Errorf("GetPreAwardTemplate(%v) error(%v)", templateid, err)
	}
	var platform int32
	var zoneid int32

	if roleId != "" {
		rto, err := model.GetRoleIdToOpenid(database.GetNacc(), openid, roleId, clientid)
		if err != nil {
			return fmt.Errorf("GetRoleIdToOpenid(%v,%v,%v) templateid(%v) error(%v)", openid, roleId, clientid, templateid, err)
		}
		platform = rto.Platform
		zoneid = rto.Zoneid
	}

	xlog.FromContext(ctx).Info(fmt.Sprintf("emailPigeon(%v,%v,%v,%v,%v,%v)", openid, roleId, clientid, templateid, platform, zoneid))

	return model.CreatePreAward(database.GetNaward(), pat, openid, platform, zoneid)
}

func Redeem(ctx context.Context, surveyId, openid, roleId, clientid, head string, awardInfo *proto.AwardInfo) {
	awardInfo.Type = model.GiveOutTypeRedeem
	var ret string
	redeem, err := redeemPigeon(clientid, head)
	if err != nil {
		ret = "false"
		xlog.FromContext(ctx).Error(fmt.Sprintf("RedeemPigeon(%v,%v) error(%v)", clientid, head, err))
		// 放入重试队列
		if err := awarder.Push(surveyId, openid, roleId, clientid, head, strconv.FormatInt(int64(awardInfo.SurveyRecordId), 10), awardInfo.Type); err != nil {
			xlog.FromContext(ctx).Error(fmt.Sprintf("push(%v,%v,%v,%v,%v,%v) error(%v)", surveyId, openid, roleId, clientid, head, awardInfo, err))
		}
	} else {
		ret = "true"
		awardInfo.Value = redeem
		err := SetAwardInfo(ctx, surveyId, openid, roleId, awardInfo)
		if err != nil {
			xlog.FromContext(ctx).Error(fmt.Sprintf("SetAwardInfo(%v,%v,%v,%v) error(%v)", surveyId, openid, roleId, awardInfo, err))
		}

	}
	shared.MetricAward.Inc(clientid, model.GiveOutTypeRedeem, ret)
}

func redeemPigeon(clientid, head string) (string, error) {
	cc, err := model.GetClientConfig(clientid)
	if err != nil {
		return "", err
	}
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	val := &url.Values{}
	val.Set("clientid", cc.GetClientid())
	val.Set("timestamp", timestamp)
	val.Set("sig", shared.PigeonSig(cc.GetSecret(), timestamp))
	val.Set("head", head)
	start := time.Now()
	var e error
	defer func() {
		code := 0
		if e != nil {
			code = -1
		}
		costTime := time.Since(start)
		shared.MetricCallHttpAPICostTime.Observe(float64(costTime), config.Get().RedeemFeed, xcast.ToString(code))
	}()

	resp, e := redeemDialer.Get(config.Get().RedeemFeed + "?" + val.Encode())
	if e != nil {
		return "", e
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("redeem error with status(%d)", resp.StatusCode)
	}

	var ret struct {
		Ret    int    `json:"ret"`
		Redeem string `json:"redeem"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&ret); err != nil {
		return "", err
	}
	if ret.Ret != 0 {
		return "", fmt.Errorf("redeem error with return(%d)", ret.Ret)
	}

	return ret.Redeem, nil
}

func GetLastAwardInfo(ctx context.Context, surveyId, openid, roleid string) (*proto.AwardInfo, error) {
	clt := database.GetRedis()
	b, err := clt.Get(ctx, fmt.Sprintf("survey|%s|%s|%s", surveyId, openid, roleid)).Bytes()
	if err != nil {
		return nil, err
	}
	awardInfo := new(proto.AwardInfo)
	err = json.Unmarshal(b, awardInfo)
	return awardInfo, err
}

func SetAwardInfo(ctx context.Context, surveyId, openid, roleid string, awardInfo *proto.AwardInfo) error {
	b, err := json.Marshal(awardInfo)
	if err != nil {
		return err
	}
	ttl := config.Get().AwardInfoTTL
	if ttl == 0 {
		ttl = 24 * 7 * time.Hour
	}
	clt := database.GetRedis()
	return clt.Set(ctx, fmt.Sprintf("survey|%s|%s|%s", surveyId, openid, roleid), b, ttl).Err()
}

type codec struct{}

// Marshal marshal context data to []byte
func (codec) Marshal(v interface{}) ([]byte, error) {
	body, ok := v.([]byte)
	if !ok {
		return nil, errors.New("invalid type")
	}
	return body, nil
}

// Unmarshal unmarshal []byte to context data
func (codec) Unmarshal(b []byte) (interface{}, error) {
	return b, nil
}

func PushAward(ctx context.Context, surveyId, openid, roleId, clientid string, cfg *proto.CmsSurveyVersion_PushAwardConfig, awardInfo *proto.AwardInfo) {
	awardInfo.Type = model.GiveOutTypePush
	var ret string
	templateId := fmt.Sprintf("%s@@%s@@%d", cfg.SceneCode, cfg.ChannelCode, cfg.StrategyId)
	err := push.StrategyInvoke(ctx, &push.StrategyInvokeParam{
		SceneCode:   cfg.SceneCode,
		ChannelCode: cfg.ChannelCode,
		StrategyID:  cfg.StrategyId,
		Users: []*push.StrategyInvokeUser{
			&(push.StrategyInvokeUser{
				OpenID: xcast.ToInt64(openid),
				RoleID: xcast.ToInt64(roleId),
			}),
		},
		ClientID: xcast.ToInt64(clientid),
	})
	if err != nil {
		ret = "false"
		xlog.FromContext(ctx).Error(fmt.Sprintf("PushStrategyInvoke(%v,%v) error(%v)", openid, roleId, err))
		// 放入重试队列
		if err := awarder.Push(surveyId, openid, roleId, clientid, templateId, strconv.FormatInt(int64(awardInfo.SurveyRecordId), 10), awardInfo.Type); err != nil {
			xlog.FromContext(ctx).Error(fmt.Sprintf("push(%v,%v,%v,%v,%v,%v) error(%v)", surveyId, openid, roleId, clientid, templateId, awardInfo, err))
		}
	} else {
		ret = "true"
		err := SetAwardInfo(ctx, surveyId, openid, roleId, awardInfo)
		if err != nil {
			xlog.FromContext(ctx).Error(fmt.Sprintf("SetAwardInfo(%v,%v,%v,%v) error(%v)", surveyId, openid, roleId, awardInfo, err))
		}
	}
	shared.MetricAward.Inc(clientid, model.GiveOutTypePush, ret)
}
