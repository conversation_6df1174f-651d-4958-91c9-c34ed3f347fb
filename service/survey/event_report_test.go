package survey

import (
	"context"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestRealTimeEventUpLogReport(t *testing.T) {
	Convey("test realtime report", t, func(c C) {
		url := "http://realtime_event:8090/v1/realtime_event/event/uplog"
		req := &UpLogRequest{
			ClientId:  1033,
			EntityId:  "1111111",
			UserType:  2,
			EventKey:  "finished_survey_ids",
			EventTime: time.Now().Unix(),
			Vals:      []*UpLogRequestVal{{Key: "survey_id", Val: "77"}},
		}
		err := report(context.Background(), req, url)
		c.So(err, ShouldBeNil)
	})
}
