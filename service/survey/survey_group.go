package survey

import (
	"context"

	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/survey/model"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/shared"
)

func CheckSurveyGroupStatus(ctx context.Context, groupId string, scene string) (*proto.CmsSurveyGroupVersion, error) {
	logger := xlog.FromContext(ctx)
	csg, err := model.GetCmsSurveyGroup(groupId)
	if err != nil {
		logger.Error("model.GetCmsSurveyGroup failed", xlog.String("survey_group", groupId), xlog.Err(err))
		return nil, shared.EcodeSurveyGroupNotExisted
	}
	shared.MetricSurveyGroupAllReqTotal.Inc(csg.Clientid, xcast.ToString(csg.Id), groupId, csg.Name, scene)
	if csg.IsDelete == 1 {
		return nil, shared.EcodeSurveyGroupDeleted
	}
	if csg.IsPublish == 0 {
		return nil, shared.EcodeSurveyGroupNotExisted
	}
	return csg, nil
}
