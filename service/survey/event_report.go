package survey

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/shared"
)

var (
	evClient = xresty.New()
)

type UpLogRequest struct {
	ClientId  int64              `json:"client_id"` // 租户
	EntityId  string             `json:"entity_id"` // 账号信息
	UserType  int32              `json:"user_type"` // 账号类型 1 openid 2 roleid
	EventKey  string             `json:"event_key"` // 实时服务颁发给业务侧使用
	EventTime int64              `json:"event_time"`
	Vals      []*UpLogRequestVal `json:"vals"`
}

type UpLogRequestVal struct {
	Key string `json:"key"`
	Val string `json:"val"`
}

// 实时平台数据资格数据上报
func RealTimeEventUpLogReport(ctx context.Context, clientId int64,
	surveyId int32, nid, roleId string, periodEndtime int64) error {

	// 不在制定客户端内，不上报
	if !shared.Contains(config.Get().RealTimeEvent.InClientIds, clientId) {
		return nil
	}

	if roleId != "" {
		upLogUrl := config.Get().RealTimeEvent.Host + config.Get().RealTimeEvent.UpLog
		nowUnix := time.Now().Unix()

		// 如果开关关闭，就不再上报这个了
		if !config.Get().RealTimeEvent.HiddenKey {
			req := &UpLogRequest{
				ClientId:  clientId,
				EntityId:  roleId,
				UserType:  2,
				EventKey:  config.Get().RealTimeEvent.Key,
				EventTime: nowUnix,
				Vals:      []*UpLogRequestVal{{Key: "survey_id", Val: xcast.ToString(surveyId)}},
			}
			err := report(ctx, req, upLogUrl)
			if err != nil {
				return err
			}
		}

		if !config.Get().RealTimeEvent.HiddenPeriodKey {
			//频控上报
			req2 := &UpLogRequest{
				ClientId:  clientId,
				EntityId:  roleId,
				UserType:  2,
				EventKey:  config.Get().RealTimeEvent.PeriodKey,
				EventTime: nowUnix,
				Vals: []*UpLogRequestVal{
					{Key: "survey_id", Val: xcast.ToString(surveyId)},
					{Key: "limit_until_timestamp", Val: xcast.ToString(periodEndtime)},
				},
			}
			err := report(ctx, req2, upLogUrl)
			if err != nil {
				return err
			}
		}

	}
	return nil
}

func report(ctx context.Context, request *UpLogRequest, url string) error {
	bs, err := json.Marshal(request)
	if err != nil {
		return err
	}
	start := time.Now()
	res := new(xgin.Return)
	requestId := ""
	ginCtx := xgin.FromContext(ctx)
	if ginCtx != nil {
		requestId = xnet.GetRequestId(xgin.FromContext(ctx).Request)
	}
	r, err := evClient.R().
		SetHeaders(map[string]string{"Content-Type": "application/json",
			xnet.RequestId: requestId}).
		SetBody(bs).SetResult(res).Post(url)
	defer func() {
		code := 0
		if err != nil {
			code = -1
		}
		costTime := time.Since(start)
		shared.MetricCallHttpAPICostTime.Observe(float64(costTime),
			url, xcast.ToString(code))
	}()

	if err != nil {
		return fmt.Errorf("UpLog api error:%+v request:%s response:%+v statusCode:%d",
			err, bs, res, r.StatusCode())
	}

	if res.Code != 0 {
		return fmt.Errorf("UpLog api fail request:%s response:%+v", bs, res)
	}
	return nil
}
