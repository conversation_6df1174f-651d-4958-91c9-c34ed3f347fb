package survey

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xtime"

	"gitlab.papegames.com/fringe/survey/shared/geoip"

	"github.com/golang/snappy"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/retry"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdefer"
	"gitlab.papegames.com/fringe/sparrow/pkg/xencoding/json"
	"gitlab.papegames.com/fringe/sparrow/pkg/xhash"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/retryable"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/model"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/service/award"
	"gitlab.papegames.com/fringe/survey/shared"
	"gitlab.papegames.com/fringe/survey/shared/mq"
	"go.uber.org/zap"
)

var (
	ErrSurveyNotStarted    = errors.New("survey not started")
	ErrSurveyPaused        = errors.New("survey paused")
	ErrSurveyOver          = errors.New("survey over")
	ErrSurveyAnswered      = errors.New("survey answered")
	ErrSurveyDeleted       = errors.New("survey deleted")
	ErrSurveyLimited       = errors.New("survey limited")
	ErrSurveyNotExisted    = errors.New("survey not existed")
	ErrTokenVerifyFailed   = errors.New("token verify failed")
	ErrSurveyConfigInvaild = errors.New("survey config invalid")
	ErrIpNotFound          = errors.New("ip not found")
)
var sur *Survey

func Startup() error {
	conf := config.Get()

	retryStrategy := &retry.Exponential{
		Delay:      5 * time.Second,
		Multiplier: 1.6,
		Jitter:     0.2,
		MaxDelay:   30 * time.Second,
	}

	sur = new(Survey)
	sur.do = retryable.New(
		retryable.WithImmediately(conf.Survey.Immediately),
		retryable.WithName(conf.Survey.Name),
		retryable.WithSharding(conf.Survey.Sharding),
		retryable.WithBatch(conf.Survey.Batch),
		retryable.WithResting(conf.Survey.Resting),
		retryable.WithBarrier(conf.Survey.Barrier),
		retryable.WithParallel(conf.Survey.Parallel),
		retryable.WithCodec(new(codec)),
		retryable.WithStorage(retryable.NewStorage("redis", database.GetRedis())),
		retryable.WithBackoff(
			func(r *retryable.Record) time.Duration {
				return retryStrategy.Backoff(r.Retries)
			},
		),
	)

	sur.do.Do = sur.InsertDetail
	xdefer.Register(sur.do.Close)
	return sur.do.Start()
}

type Survey struct {
	do *retryable.Retryable
}

// Push survey into retryable.
func Push(surveyId, surveyRecordId, ip string, surveyData []byte) error {
	return sur.Push(surveyId, surveyRecordId, ip, surveyData)
}

func (s *Survey) Push(surveyId, surveyRecordId, ip string, surveyData []byte) error {
	err := s.do.Push(surveyId+"|"+surveyRecordId+"|"+ip, surveyData)
	if err != nil {
		return err
	}
	return nil
}

const maxRetries = 10
const periodicForever = 253370772000 // 频控最大持续时间 - 秒 9999-01-01 10:00:00

func (s *Survey) InsertDetail(r *retryable.Record) error {
	// logger := xlog.GetEntry().SetPrefix("Survey(" + r.Key + ")")
	// defer xlog.PutEntry(logger)
	logger := xlog.S().With(zap.String("SurveyKey", r.Key))

	if r.Retries >= maxRetries {
		logger.Error("retryable: survey(%s) out of retries(%d)", r.Key, r.Retries)
		return retryable.ErrShouldAbortRetry
	}

	ids := strings.SplitN(r.Key, "|", 3)
	if len(ids) != 3 {
		err := fmt.Errorf("retryable: survey(%s) invalid key", r.Key)
		logger.Error(err.Error())
		return err
	}

	_, surveyRecordId, ip := ids[0], ids[1], ids[2]

	data, ok := r.RawData.([]byte)
	if !ok {
		err := fmt.Errorf("retryable: RawData(%s) Type must be []byte", r.Key)
		logger.Error(err.Error())
		return retryable.ErrShouldAbortRetry
	}

	info, err := Decode(data)
	if err != nil {
		err = fmt.Errorf("retryable: Survey.Decode(%s) with error(%s)", r.Key, err)
		logger.Error(err.Error())
		return retryable.ErrShouldAbortRetry
	}

	srid, _ := strconv.ParseInt(surveyRecordId, 10, 64)

	surveyRecords := make([]*proto.SurveyRecordDetail, 0, len(info.SurveyRecords))
	var tlogs []*mq.SurveyRecordDetails
	// 获取问卷配置
	cs, err := model.GetCmsSurvey(info.SurveyId)
	if err != nil {
		logger.Error(fmt.Sprintf("model.GetCmsSurvey(%v) with error(%v)", info.SurveyId, err))
		return retryable.ErrShouldAbortRetry
	}

	//use key map
	kyMap := cs.KeyValueMap
	for question, records := range info.SurveyRecords {
		qContent := kyMap[question]
		for _, record := range records {
			surveyRecords = append(surveyRecords, &proto.SurveyRecordDetail{
				SurveyRecordId: int32(srid),
				Question:       question,
				Option:         record.Option,
				Text:           record.Text,
			})

			//数值型，省市区类型 key value中没有匹配项, 多选排序会是 hash_code|1 这种
			var optionContent string
			rawOption := record.Option
			if strings.Index(rawOption, "|") > -1 {
				splits := strings.SplitN(rawOption, "|", 2)
				if len(splits) >= 2 {
					if optionContentTmp, ok := kyMap[splits[0]]; ok {
						optionContent = optionContentTmp + "|" + splits[1] // 题目选项|数字
					} else {
						logger.Warn(fmt.Sprintf("record.Option keyMap not exist, option:%s", rawOption))
					}
				}
			} else {
				optionContent = kyMap[rawOption]
			}

			if optionContent == "" {
				optionContent = record.Option
			}

			tlogs = append(tlogs, &mq.SurveyRecordDetails{
				ClientId:        cs.Clientid,
				SurveyId:        int64(cs.SurveyId),
				SurveyName:      cs.Name,
				HashCode:        cs.HashCode,
				VOpenId:         info.Openid,
				VRoleId:         info.RoleId,
				DeviceId:        info.DeviceId,
				Ip:              ip,
				ClientIP:        ip,
				SurveyRecordId:  int32(srid),
				Question:        question,
				QuestionContent: qContent,
				Option:          record.Option,
				OptionContent:   optionContent,
				Text:            record.Text,
				StartTime:       cs.Stime.AsTime().Local().Format(time.DateTime),
				EndTime:         cs.Etime.AsTime().Local().Format(time.DateTime),
			})
		}
	}
	mq.UploadSurveyRecordDetails(context.Background(), tlogs)
	d := database.Get()
	if err := model.InsertSurveyRecordDetails(d,
		info.SurveyId,
		surveyRecords,
	); err != nil {
		err = fmt.Errorf("retryable: model.InsertSurveyRecordDetails(%s) with error(%s)", r.Key, err)
		logger.Error(err.Error())
		return err
	}
	return nil
}

func Decode(b []byte) (*model.SurveyInfo, error) {
	b, err := snappy.Decode(nil, b)
	if err != nil {
		return nil, err
	}

	info := new(model.SurveyInfo)
	if err := json.Unmarshal(b, info); err != nil {
		return nil, err
	}
	return info, nil
}

type codec struct{}

// Marshal marshal context data to []byte
func (codec) Marshal(v interface{}) ([]byte, error) {
	body, ok := v.([]byte)
	if !ok {
		return nil, errors.New("invalid type")
	}
	return body, nil
}

// Unmarshal unmarshal []byte to context data
func (codec) Unmarshal(b []byte) (interface{}, error) {
	return b, nil
}

// 登录接口
func Signin(ctx context.Context, req *proto.SigninRequest, ip string) (*proto.SigninResponse, error) {
	logger := xlog.FromContext(ctx)
	db := xgorm.FromContext(ctx)
	var csg *proto.CmsSurveyGroupVersion
	if req.GetGroup() != "" {
		//检查问卷组状态
		var err error
		csg, err = CheckSurveyGroupStatus(ctx, req.GetGroup(), "signin")
		if err != nil {
			return &proto.SigninResponse{}, err
		}
	}

	cs, err := model.GetCmsSurvey(req.SurveyId)
	if err != nil {
		logger.Error("model.GetCmsSurvey failed", xlog.String("survey", req.SurveyId), xlog.Err(err))
		return &proto.SigninResponse{}, shared.EcodeSurveyNotExisted
	}
	shared.MetricSurveyAllReqTotal.Inc(cs.Clientid, xcast.ToString(cs.SurveyId), cs.HashCode, cs.Name, "signin")
	// 判断是否生效中
	err = model.CmsSurveyInEffect(ctx, cs, req.Timezone)
	if err != nil {
		logger.Error("model.CmsSurveyInEffect failed", xlog.Err(err))
		return &proto.SigninResponse{}, err
	}

	// 获取问卷类型
	surveyType, err := model.GetCmsSurveyType(ctx, cs)
	if err != nil {
		logger.Error("model.GetCmsSurveyType failed", xlog.Err(err))
		return &proto.SigninResponse{}, err
	}

	// 获取问卷答题次数
	surveyAnswerTimes, err := model.GetCmsSurveyAnswerTimes(ctx, cs)
	if err != nil {
		logger.Error("model.GetCmsSurveyAnswerTimes failed", xlog.Err(err))
		return &proto.SigninResponse{}, err
	}

	ss := &model.SurveySelector{
		SurveyId:          req.SurveyId,
		SurveyType:        surveyType,
		SurveyAnswerTimes: surveyAnswerTimes,
		Openid:            req.Openid,
		RoleId:            req.RoleId,
		DeviceId:          req.DeviceId,
		Ip:                ip,
	}

	var needAuth bool
	var needVerify bool

	//判断问卷组限制类型
	if csg != nil {
		switch csg.LimitType {
		case proto.CmsSurveyGroupVersion_RoleIdOnce, proto.CmsSurveyGroupVersion_OpenIdOnce:
			needAuth = true
			needVerify = true
		}
	}

	switch surveyType {
	case model.SurveyTypeRoleId, model.SurveyTypeOpenid:
		// Token验证
		if ss.Openid == "" || req.Token == "" {
			logger.Error("openid is empty", xlog.String("openid", ss.Openid), xlog.String("token", req.Token))
			return &proto.SigninResponse{}, shared.EcodeTokenVerifyFailed
		}
		//需要roleid问卷，roleid为空值不给登录
		if surveyType == model.SurveyTypeRoleId && (ss.RoleId == "" || ss.RoleId == "0") {
			logger.Error("roleid is empty",
				xlog.String("openid", ss.Openid),
				xlog.String("roleid", ss.RoleId),
				xlog.String("token", req.Token))
			return &proto.SigninResponse{}, shared.EcodeTokenVerifyFailed
		}
		needAuth = true
		needVerify = true
	}

	if needVerify {
		if err := shared.Verify(ss.Openid, req.Token); err != nil {
			logger.Error("shared.Verify faild", xlog.Err(err))
			return &proto.SigninResponse{}, shared.EcodeTokenVerifyFailed
		}
	}

	var overflowed bool
	//如果有问卷组
	if csg != nil {
		overflowed, err = model.SurveyGroupRecordOverflowed(db, csg, ss)
		if err != nil {
			return &proto.SigninResponse{}, err
		}
		if overflowed {
			return &proto.SigninResponse{}, shared.EcodeSurveyGroupAnswered
		}
	}

	overflowedHandle := func(answeredErr, limitErr error) (*proto.SigninResponse, error) {
		switch surveyType {
		case model.SurveyTypeRoleId, model.SurveyTypeOpenid:
			var authority string
			if needAuth {
				authority, err = shared.Sign(&jwt.Claims{
					ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
					Id:        req.Openid,
				})
				if err != nil {
					logger.Error("shared.Sign failed", xlog.Err(err))
					return &proto.SigninResponse{}, answeredErr
				}
			}
			awardInfo, err2 := award.GetLastAwardInfo(ctx, ss.SurveyId, ss.Openid, ss.RoleId)
			if err2 != nil {
				return &proto.SigninResponse{
					Authority: authority,
				}, answeredErr
			}
			// 获取发奖信息
			return &proto.SigninResponse{
				AwardInfo: awardInfo,
				Sign:      Sign(cs.Clientid, ss.SurveyId, awardInfo.SurveyRecordId, ss.Openid, ss.RoleId),
				Authority: authority,
			}, answeredErr
		default:
			return &proto.SigninResponse{}, limitErr
		}
	}

	// 开启了答题频率限制
	if checkPeriodicControlEnable(cs) {
		// 答题周期限制
		durationLimit, _, err := answerPeriodicLimit(ctx, ss, cs)
		if err != nil {
			return &proto.SigninResponse{}, err
		}
		if durationLimit {
			return overflowedHandle(shared.EcodeSurveyAnswered, shared.EcodeSurveyLimited)
			//return overflowedHandle(shared.EcodeSurveyDurationAnswered, shared.EcodeSurveyDurationLimited)
		}
	} else {
		// 判断是否超过答题限制
		overflowed, err = model.SurveyRecordOverflowed(db, ss)
		if err != nil {
			return &proto.SigninResponse{}, err
		}

		// 如果已超过答题限制
		if overflowed {
			return overflowedHandle(shared.EcodeSurveyAnswered, shared.EcodeSurveyLimited)
		}
	}

	if needAuth {
		authority, err := shared.Sign(&jwt.Claims{
			ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
			Id:        req.Openid,
		})
		if err != nil {
			logger.Error("shared.Sign failed", xlog.Err(err))
			return &proto.SigninResponse{}, ecode.Internal
		}
		return &proto.SigninResponse{
			Authority: authority,
		}, nil
	}
	return &proto.SigninResponse{}, nil
}

func Submit(ctx context.Context, req *proto.SubmitSurveyRequest, ip string, authority string) (*proto.SubmitSurveyResponse, error) {
	logger := xlog.FromContext(ctx)
	db := xgorm.FromContext(ctx)

	//判断问卷组状态
	var csg *proto.CmsSurveyGroupVersion
	if req.Group != "" {
		var err error
		csg, err = CheckSurveyGroupStatus(ctx, req.Group, "submit")
		if err != nil {
			return &proto.SubmitSurveyResponse{}, err
		}
		shared.MetricSurveyGroupAllReqTotal.Inc(csg.Clientid, xcast.ToString(csg.GroupId), csg.HashCode, csg.Name, "submit")
	}

	// 解包
	info, err := Decode(req.Body)
	if err != nil {
		logger.Warn("survey decode fail", xlog.Err(err))
		return nil, shared.EcodeSurveyUnMarshal
	}

	logger.Info("submit_survey:", xlog.Any("info", info))

	// 获取问卷配置
	cs, err := model.GetCmsSurvey(info.SurveyId)
	if err != nil {
		logger.Error("model.GetCmsSurvey failed", xlog.String("survey_id", info.SurveyId), xlog.Err(err))
		return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyNotExisted
	}
	shared.MetricSurveyAllReqTotal.Inc(cs.Clientid, xcast.ToString(cs.SurveyId), cs.HashCode, cs.Name, "submit")

	// 判断是否生效中
	err = model.CmsSurveyInEffect(ctx, cs, req.Timezone)
	if err != nil {
		logger.Error("model.CmsSurveyInEffect failed", xlog.Err(err))
		return &proto.SubmitSurveyResponse{}, err
	}

	// 问卷类型
	surveyType, err := model.GetCmsSurveyType(ctx, cs)
	if err != nil {
		logger.Error("model.GetCmsSurveyType failed", xlog.Err(err))
		return &proto.SubmitSurveyResponse{}, err
	}
	var needAuth bool
	if csg != nil {
		switch csg.LimitType {
		case proto.CmsSurveyGroupVersion_RoleIdOnce, proto.CmsSurveyGroupVersion_OpenIdOnce:
			needAuth = true
		}
	}
	// 校验authority
	switch surveyType {
	case model.SurveyTypeRoleId, model.SurveyTypeOpenid:
		//需要roleid问卷，roleid不能为空
		if surveyType == model.SurveyTypeRoleId && (info.RoleId == "" || info.RoleId == "0") {
			logger.Error("roleid is empty",
				xlog.String("openid", info.Openid),
				xlog.String("roleid", info.RoleId))
			return &proto.SubmitSurveyResponse{}, shared.EcodeTokenVerifyFailed
		}
		needAuth = true
	}

	if needAuth {
		err := checkAuthority(authority, info.Openid)
		if err != nil {
			logger.Error("CheckAuth failed", xlog.Err(err))
			return &proto.SubmitSurveyResponse{}, shared.EcodeTokenVerifyFailed
		}
	}

	// 获取问卷答题次数
	surveyAnswerTimes, err := model.GetCmsSurveyAnswerTimes(ctx, cs)
	if err != nil {
		logger.Error("model.GetCmsSurveyAnswerTimes failed", xlog.Err(err))
		return &proto.SubmitSurveyResponse{}, err
	}

	// ip
	info.Ip = ip
	// 问卷类型
	info.SurveyType = surveyType
	// 问卷答题次数
	info.SurveyAnswerTimes = surveyAnswerTimes

	// 并发锁，防止并发提交会导致的重复发奖
	if csg != nil {
		if err := surveyGroupLock(ctx, csg, info); err != nil {
			return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyGroupSubmitTooFast
		}
		overflowed, err := model.SurveyGroupRecordOverflowed(db, csg, &info.SurveySelector)
		if err != nil {
			return &proto.SubmitSurveyResponse{}, err
		}
		if overflowed {
			return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyGroupAnswered
		}
	}
	if err := lock(ctx, info); err != nil {
		return &proto.SubmitSurveyResponse{}, shared.EcodeSurveySubmitTooFast
	}

	overflowedHandle := func(answeredErr, limitErr error) (*proto.SubmitSurveyResponse, error) {
		switch surveyType {
		case model.SurveyTypeRoleId, model.SurveyTypeOpenid:
			awardInfo, err2 := award.GetLastAwardInfo(ctx, info.SurveyId, info.Openid, info.RoleId)
			if err2 != nil {
				return &proto.SubmitSurveyResponse{}, answeredErr
			}
			// 获取发奖信息
			return &proto.SubmitSurveyResponse{
				AwardInfo: awardInfo,
				Sign:      Sign(cs.Clientid, info.SurveyId, awardInfo.SurveyRecordId, info.Openid, info.RoleId),
			}, answeredErr
		default:
			return &proto.SubmitSurveyResponse{}, limitErr
		}
	}

	// 频控结束时间戳
	var periodEndtime int64

	// 开启了答题频率限制
	if checkPeriodicControlEnable(cs) {
		// 答题周期限制
		durationLimit, durationTm, err := answerPeriodicLimit(ctx, &info.SurveySelector, cs)
		if err != nil {
			return &proto.SubmitSurveyResponse{}, err
		}
		if durationLimit {
			return overflowedHandle(shared.EcodeSurveyAnswered, shared.EcodeSurveyLimited)
			//return overflowedHandle(shared.EcodeSurveyDurationAnswered, shared.EcodeSurveyDurationLimited)
		}
		periodEndtime = time.Now().Unix() + int64(durationTm)
	} else {
		// 判断是否超过答题限制
		overflowed, err := model.SurveyRecordOverflowed(db, &info.SurveySelector)
		if err != nil {
			return &proto.SubmitSurveyResponse{}, err
		}

		// 如果已超过答题限制
		if overflowed {
			return overflowedHandle(shared.EcodeSurveyAnswered, shared.EcodeSurveyLimited)
		}
		periodEndtime = periodicForever
	}

	var isValid int32
	// 角色登录下才作区服校验
	switch surveyType {
	case model.SurveyTypeRoleId:
		zoneIds, err := model.GetCmsSurveyZoneIds(ctx, cs)
		if err != nil {
			logger.Error("model.GetCmsSurveyZoneIds failed", xlog.Err(err))
			return &proto.SubmitSurveyResponse{}, err
		}
		// 0是有效，1是无效，CMS统计那边反着来了
		if len(zoneIds) > 0 {
			rto, err := model.GetRoleIdToOpenid(database.GetNacc(), info.Openid, info.RoleId, cs.GetClientid())
			if err != nil {
				xlog.FromContext(ctx).Error("model.GetRoleIdToOpenid with error",
					xlog.Err(err),
					xlog.String("openid", info.Openid),
					xlog.String("roleid", info.RoleId),
					xlog.String("clientid", cs.GetClientid()))
				return &proto.SubmitSurveyResponse{}, err
			}
			isValid = 1
			// 如果角色所在区服不在白名单中，则标记为无效
			for _, zoneId := range zoneIds {
				if rto.Zoneid == zoneId {
					isValid = 0
					break
				}
			}
		}
	}

	ipLocal, err := geoip.GetIpLocal(info.Ip)
	if err != nil {
		logger.Info("config.GetIpLocal failed", xlog.Err(err))
		ipLocal = &geoip.Location{}
	}
	extra := map[string]any{
		"location": ipLocal,
		"platid":   req.GetPlatid(),
	}
	extraStr, err := json.Marshal(extra)
	if err != nil {
		logger.Error("json.Marshal failed", xlog.Err(err))
	}

	if csg != nil {
		//创建问卷组答题记录
		surveyGroupRecore := &proto.SurveyGroupRecord{
			Openid:   info.Openid,
			RoleId:   info.RoleId,
			DeviceId: info.DeviceId,
			Ip:       info.Ip,
			Ctime:    xtype.NewTimestamp(time.Now()),
		}
		_, err := model.CreateSurveyGroupRecord(db, csg.HashCode, surveyGroupRecore)
		if err != nil {
			logger.Error("model.CreateSurveyGroupRecord failed", xlog.Err(err))
			return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyGroupRecordCreateFailed
		}
	}

	// 创建答题记录
	surveyRecord := &proto.SurveyRecord{
		Openid:    info.Openid,
		RoleId:    info.RoleId,
		DeviceId:  info.DeviceId,
		Ip:        info.Ip,
		IsValid:   0,
		BeginTime: xtype.NewTimestamp(info.BeginTime),
		EndTime:   xtype.NewTimestamp(info.EndTime),
		Ctime:     xtype.NewTimestamp(time.Now()),
		Extra:     string(extraStr),
	}
	info.Extra = surveyRecord.Extra
	if info.Falg1 == 1 || isValid == 1 {
		surveyRecord.IsValid = 1
	}

	surveyRecordId, err := model.CreateSurveyRecord(db, info.SurveyId, surveyRecord)
	if err != nil {
		logger.Error("model.CreateSurveyRecord failed", xlog.Err(err))
		return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyRecordCreateFailed
	}

	awardInfo := &proto.AwardInfo{
		SurveyRecordId: surveyRecordId,
	}
	var sign string

	switch surveyType {
	case model.SurveyTypeRoleId, model.SurveyTypeOpenid:
		// 有效答题才会有发奖和签名
		if isValid == 0 {
			setting := cs.GetSettingStruct()
			if setting == nil {
				logger.Error("cs.GetSettingStruct failed", xlog.Err(err))
				return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyConfigInvalid
			}
			gift := setting.GetGiftConfig()
			if gift == nil {
				logger.Error("setting.GetGiftConfig failed", xlog.Err(err))
				return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyConfigInvalid
			}
			if gift.GetIsGiveOutByCms() {
				switch gift.GetGiveOutType() {
				case model.GiveOutTypeEmail:
					emailcfg := gift.GetPreAwardConfig()
					if emailcfg == nil {
						logger.Error("gift.GetPreAwardConfig() is nil")
						return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyConfigInvalid
					}

					// 邮件发奖
					award.Email(ctx, info.SurveyId, info.Openid, info.RoleId, cs.GetClientid(), emailcfg.GetId(), awardInfo)
				case model.GiveOutTypeRedeem:
					awardcfg := gift.GetRedeemConfig()
					if awardcfg == nil {
						logger.Error("gift.GetRedeemConfig() is nil")
						return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyConfigInvalid
					}
					// 兑换码发奖
					award.Redeem(ctx, info.SurveyId, info.Openid, info.RoleId, cs.GetClientid(), awardcfg.GetRedeemHead(), awardInfo)
				case model.GiveOutTypePush:
					pushCfg := gift.GetPushAwardConfig()
					if pushCfg == nil {
						logger.Error("gift.GetPushAwardConfig() is nil")
						return &proto.SubmitSurveyResponse{}, shared.EcodeSurveyConfigInvalid
					}
					// 发送推送邮件奖励
					award.PushAward(ctx, info.SurveyId, info.Openid, info.RoleId, cs.GetClientid(), pushCfg, awardInfo)
				}
			} else {
				// 设置发奖信息
				err := award.SetAwardInfo(ctx, info.SurveyId, info.Openid, info.RoleId, awardInfo)
				if err != nil {
					logger.Error("SetAwardInfo failed", xlog.String("survey_id", info.SurveyId),
						xlog.String("openid", info.Openid), xlog.String("roleid", info.RoleId),
						xlog.Any("award_info", awardInfo), xlog.Err(err))
				}
			}
			sign = Sign(cs.Clientid, info.SurveyId, awardInfo.SurveyRecordId, info.Openid, info.RoleId)
		}
	}
	tlog := &mq.SurveyTLog{
		ClientID:       cs.Clientid,
		SurveyID:       fmt.Sprintf("%d", cs.SurveyId),
		SurveyName:     cs.Name,
		STime:          cs.Stime.AsTime().Format(time.DateTime),
		ETime:          cs.Etime.AsTime().Format(time.DateTime),
		HashCode:       cs.HashCode,
		SurveyRecordId: surveyRecordId,
		VOpenID:        info.Openid,
		VRoleID:        info.RoleId,
		DeviceID:       info.DeviceId,
		IP:             ip,
		ClientIP:       ip,
		IsValid:        surveyRecord.IsValid,
		IsDelete:       0,
		TimeZone:       req.Timezone,
		BeginTime:      info.BeginTime.Local().String(),
		EndTime:        info.EndTime.Local().String(),
		Extra:          info.Extra,
		CreateTime:     time.Now().Format(time.DateTime),
		AnswerValid:    info.Falg1,
		PlatId:         req.GetPlatid(),
	}
	mq.UploadSurvey(ctx, tlog)
	// 异步任务,插入detail
	err = Push(info.SurveyId, strconv.FormatInt(int64(awardInfo.SurveyRecordId), 10), ip, req.Body)
	if err != nil {
		logger.Error("push detail failed",
			xlog.String("survey_id", info.SurveyId),
			xlog.Int32("survey_record_id", awardInfo.SurveyRecordId),
			xlog.Err(err))
	}

	// 实时平台数据资格数据上报
	err = RealTimeEventUpLogReport(ctx, xcast.ToInt64(cs.Clientid), cs.SurveyId, info.Openid, info.RoleId, periodEndtime)
	if err != nil {
		logger.Error("Send Realtime Event fail", xlog.Err(err))
	}

	return &proto.SubmitSurveyResponse{
		AwardInfo: awardInfo,
		Sign:      sign,
	}, nil

}

func Sign(clientid, surveyId string, surveyRecordId int32, openid, roleid string) string {
	key := config.Get().Keys[clientid]
	strToSign := fmt.Sprintf("openid=%v&role_id=%v&survey_id=%v&survey_record_id=%v", openid, roleid, surveyId, surveyRecordId)
	return xhash.HMACSha256Base64([]byte(key), []byte(strToSign))
}

func GetSurveyInfo(ctx context.Context, request *proto.AnswerRequest, survey *proto.CmsSurveyVersion, ip string, authority string) (*model.SurveyInfo, error) {
	surveyType, err := model.GetCmsSurveyType(ctx, survey)
	if err != nil {
		return nil, err
	}

	// 校验authority
	switch surveyType {
	case model.SurveyTypeRoleId, model.SurveyTypeOpenid:
		if err := checkAuthority(authority, request.Openid); err != nil {
			return nil, err
		}
	}

	return model.GetSurveyInfo(xgorm.FromContext(ctx), &model.SurveySelector{
		SurveyId:   request.SurveyId,
		Openid:     request.Openid,
		RoleId:     request.RoleId,
		DeviceId:   request.DeviceId,
		Ip:         ip,
		SurveyType: surveyType,
	})
}

func checkAuthority(authority, openid string) error {
	claims, err := shared.Parse(authority)
	if err != nil {
		return err
	}
	if claims.Id != openid {
		return fmt.Errorf("authority(%v) openid(%v,%v) not match", authority, claims.Id, openid)
	}
	return nil
}

func lock(ctx context.Context, info *model.SurveyInfo) error {
	var lockKey string
	switch info.SurveyType {
	case model.SurveyTypeRoleId:
		lockKey = info.RoleId
	case model.SurveyTypeOpenid:
		lockKey = info.Openid
	case model.SurveyTypeDeviceId:
		lockKey = info.DeviceId
	case model.SurveyTypeIp:
		lockKey = info.Ip
	case model.SurveyTypeAny:
		if info.RoleId != "" {
			lockKey = info.RoleId
		} else if info.Openid != "" {
			lockKey = info.Openid
		} else if info.DeviceId != "" {
			lockKey = info.DeviceId
		} else if info.Ip != "" {
			lockKey = info.Ip
		} else {
			lockKey = "any"
		}
	}
	clt := database.GetRedis()
	ok, err := clt.SetNX(ctx, fmt.Sprintf("survey|lock|%v|%v|%v", info.SurveyId, info.SurveyType, lockKey), 1, time.Second).Result()
	if err != nil {
		return err
	}
	if !ok {
		err := fmt.Errorf("survey|lock|%v|%v|%v locking", info.SurveyId, info.SurveyType, lockKey)
		xlog.FromContext(ctx).Error(err.Error())
		return err
	}
	return nil
}

func surveyGroupLock(ctx context.Context, csg *proto.CmsSurveyGroupVersion, info *model.SurveyInfo) error {
	var lockKey string
	switch csg.LimitType {
	case proto.CmsSurveyGroupVersion_OpenIdOnce:
		lockKey = info.Openid
	case proto.CmsSurveyGroupVersion_RoleIdOnce:
		lockKey = info.RoleId
	case proto.CmsSurveyGroupVersion_Unlimited:
		lockKey = "any"
	}
	clt := database.GetRedis()
	ok, err := clt.SetNX(ctx, fmt.Sprintf("survey_group|lock|%v|%v|%v", csg.GroupId, csg.LimitType, lockKey), 1, time.Second).Result()
	if err != nil {
		return err
	}
	if !ok {
		err := fmt.Errorf("survey_group|lock|%v|%v|%v locking", csg.GroupId, csg.LimitType, lockKey)
		xlog.FromContext(ctx).Error(err.Error())
		return err
	}
	return nil
}

// 检测有没有开答题频率限制
func checkPeriodicControlEnable(cs *proto.CmsSurveyVersion) bool {
	return cs.GetSettingStruct().GetBaseRuleConfig().GetPeriodicControl().GetEnable()
}

func answerPeriodicLimit(ctx context.Context, ss *model.SurveySelector, cs *proto.CmsSurveyVersion) (bool, int32, error) {
	gLog := xlog.FromContext(ctx)

	// 周期内最多答题次数
	max := cs.GetSettingStruct().GetBaseRuleConfig().GetAnswerTimesConfig().GetTimes()
	if max < 1 {
		return false, 0, shared.EcodeSurveyConfigInvalid
	}

	// 获取频控配置
	periodicControl := cs.GetSettingStruct().GetBaseRuleConfig().GetPeriodicControl()

	// 计算频控便宜秒数
	var beforeSeconds int32
	switch periodicControl.Unit {
	case proto.CmsSurveyVersion_PeriodicControl_Unit_Minute:
		beforeSeconds = periodicControl.GetInterval() * 60
	case proto.CmsSurveyVersion_PeriodicControl_Unit_Hour:
		beforeSeconds = periodicControl.GetInterval() * 3600
	case proto.CmsSurveyVersion_PeriodicControl_Unit_Day:
		beforeSeconds = periodicControl.GetInterval() * 86400
	default:
		gLog.Error("answerDurationLimit unit invalid", xlog.Int32("unit", int32(periodicControl.Unit)))
		return false, beforeSeconds, shared.EcodeSurveyConfigInvalid
	}

	// 计算开始、结束时间
	var et = time.Now().Unix()
	var st = et - int64(beforeSeconds)
	var startTime = xtime.FormatTime(time.Unix(st, 0))
	var endTime = xtime.FormatTime(time.Unix(et, 0))

	overflowed, err := model.SurveyRecordDurationOverflowed(ctx, ss, startTime, endTime, max)
	if err != nil {
		return false, beforeSeconds, err
	}

	return overflowed, beforeSeconds, nil
}
