package shared

import (
	"unsafe"

	"gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
	"gitlab.papegames.com/fringe/survey/config"
)

func Startup() error {
	err := Reload()
	if err != nil {
		return err
	}
	return nil
}

func Reload() error {
	conf := config.Get()
	cipher, err := jwt.NewToken(
		conf.Jwt.Auth.Alg,
		conf.Jwt.Auth.Secret,
	)
	if err != nil {
		return err
	}
	pcipher.Store(unsafe.Pointer(cipher))
	return nil
}

func GetLastNChars(s string, n int) string {
	if len(s) < n {
		return s
	}
	return s[len(s)-n:]
}

func Contains[T comparable](slice []T, target T) bool {
	for _, item := range slice {
		if item == target {
			return true
		}
	}
	return false
}
