package shared

import "gitlab.papegames.com/fringe/sparrow/pkg/ecode"

var (
	EcodeSurveyNotStarted              = ecode.New(1001, "survey not started")
	EcodeSurveyPaused                  = ecode.New(1002, "survey paused")
	EcodeSurveyOver                    = ecode.New(1003, "survey over")
	EcodeSurveyAnswered                = ecode.New(1004, "survey answered")
	EcodeSurveyDeleted                 = ecode.New(1005, "survey deleted")
	EcodeSurveyLimited                 = ecode.New(1006, "survey limited")
	EcodeTokenVerifyFailed             = ecode.New(1007, "token verify failed")
	EcodeSurveyNotExisted              = ecode.New(1008, "survey not existed")
	EcodeSurveyConfigInvalid           = ecode.New(1009, "survey config invalid")
	EcodeSurveyRecordCreateFailed      = ecode.New(1010, "survey record create failed")
	EcodeSurveySubmitTooFast           = ecode.New(1011, "survey submit too fast")
	EcodeSurveyUnMarshal               = ecode.New(1012, "survey unmarshal failed")
	EcodeSurveyGroupNotExisted         = ecode.New(1013, "survey group not existed")
	EcodeSurveyGroupDeleted            = ecode.New(1014, "survey group deleted")
	EcodeSurveyGroupSubmitTooFast      = ecode.New(1015, "survey group submit too fast")
	EcodeSurveyGroupAnswered           = ecode.New(1016, "survey group answered")
	EcodeSurveyGroupRecordCreateFailed = ecode.New(1017, "survey group record create failed")
	EcodeSurveyGroupWrongSetting       = ecode.New(1018, "survey group wrong settings")
	EcodeSurveyDurationAnswered        = ecode.New(1019, "survey duration answered")
	EcodeSurveyDurationLimited         = ecode.New(1020, "survey duration limited")
)
