package shared

import (
	"fmt"
	"regexp"
	"strings"
)

var (
	htmlRe   = regexp.MustCompile(`<[^>]*>`)                                      //定义正则表达式，匹配任何HTML标签
	imgRegex = regexp.MustCompile(`<img\s+[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>`) // 定义正则表达式以匹配 img 标签
)

type ImgReplace struct {
	Raw   string
	Place string
}

func RemoveHTMLTags(s string) (string, error) {
	// 使用正则表达式替换所有HTML标签为空字符串
	str := htmlRe.ReplaceAllString(s, "")

	// 替换换行符
	str = strings.ReplaceAll(str, "\n", "")
	str = strings.ReplaceAll(str, "\t", "")
	str = strings.ReplaceAll(str, "\r", "")
	return str, nil
}

func EncodeImgHtml(htmlContent string) (string, []ImgReplace) {
	matches := imgRegex.FindAllStringSubmatch(htmlContent, -1)
	var imgMatches []ImgReplace
	// 遍历匹配结果
	for idx, match := range matches {
		// match[0] 是完整的 img 标签，match[1] 是 src 属性的值
		// fmt.Printf("Found img tag: %s, src: %s\n", match[0], match[1])
		replace := ImgReplace{
			Raw:   match[0],
			Place: fmt.Sprintf("##img##_%d", idx),
		}
		imgMatches = append(imgMatches, replace)
		htmlContent = strings.Replace(htmlContent, replace.Raw, replace.Place, 1)
	}
	return htmlContent, imgMatches
}

func DecodeImgHtml(htmlContent string, imgReplaceArr []ImgReplace) string {
	for _, replace := range imgReplaceArr {
		htmlContent = strings.Replace(htmlContent, replace.Place, replace.Raw, 1)
	}
	return htmlContent
}

// RemoveRichTextTags 移除Html标签 是否保留img标签
func RemoveRichTextTags(html string) string {
	// 过滤除了img以外的HTML标签
	html, replaces := EncodeImgHtml(html)
	html, _ = RemoveHTMLTags(html)
	html = DecodeImgHtml(html, replaces)
	return html
}
