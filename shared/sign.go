package shared

import (
	"crypto/md5"
	"encoding/hex"

	"gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/atomic"
)

var pcipher atomic.Pointer

func Sign(claims *jwt.Claims) (string, error) {
	cipher := (*jwt.Token)(pcipher.Load())
	return cipher.Sign(claims)
}

func Parse(token string) (*jwt.Claims, error) {
	cipher := (*jwt.Token)(pcipher.Load())
	return cipher.Parse(token)
}

func PigeonSig(clientSecret string, timestamp string) string {
	h := md5.New()
	_, _ = h.Write([]byte(clientSecret + timestamp))
	return hex.EncodeToString(h.Sum(nil))
}
