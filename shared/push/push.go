package push

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/survey/shared"
	"io"
	"net/http"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xhttp"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtrace"
	"gitlab.papegames.com/fringe/survey/config"
)

var (
	client = xhttp.NewClient()
)

type StrategyInvokeUser struct {
	RoleID      int64  `json:"roleid"`
	OpenID      int64  `json:"openid"`
	PhoneNumber string `json:"phone_number"`
	ZoneID      int64  `json:"zoneid"`
}

type StrategyInvokeParam struct {
	SceneCode   string                `json:"scene_code"`
	ChannelCode string                `json:"channel_code"`
	StrategyID  int64                 `json:"strategy_id"`
	Users       []*StrategyInvokeUser `json:"users"`
	ClientID    int64                 `json:"clientid"`
}

type StrategyInvokeResult struct {
	Code      int    `json:"code"`
	Info      string `json:"info"`
	RequestID string `json:"request_id"`
	Data      struct {
		ResList []*struct {
			User        *StrategyInvokeUser `json:"user"`
			ErrMsg      string              `json:"err_msg"`
			DiezhiMsgID string              `json:"diezhi_msgid"`
		} `json:"res_list"`
	} `json:"data"`
}

func StrategyInvoke(ctx context.Context, params *StrategyInvokeParam) error {
	logger := xlog.FromContext(ctx).Sugar()
	data, err := json.Marshal(params)
	if err != nil {
		logger.Error("PushStrategyInvoke Marshal failed. openid:[%d] roleid:[%d] clientid:[%d] scene_code:[%s] channel_code:[%s] strategy_id:[%d]", params.Users[0].OpenID, params.Users[0].RoleID, params.ClientID, params.SceneCode, params.ChannelCode, params.StrategyID)
		return err
	}

	host := config.Get().Push.StrategyInvoke
	start := time.Now()
	var e error
	defer func() {

		code := 0
		if e != nil {
			code = -1
		}
		costTime := time.Since(start)
		shared.MetricCallHttpAPICostTime.Observe(float64(costTime), host, xcast.ToString(code))
	}()

	request, e := http.NewRequest(http.MethodPost, host, bytes.NewReader(data))
	if e != nil {
		logger.Error(e)
		return e
	}
	request = request.WithContext(ctx)

	request.Header.Set("Content-Type", "application/json")
	if xtrace.Enable() {
		client.Transport = xhttp.NewTraceTransport(client.Transport)
	}

	response, err := client.Do(request)
	if err != nil {
		logger.Error(err)
		return err
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.Error(err)
		return err
	}

	result := &StrategyInvokeResult{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		logger.Errorf("PushStrategyInvoke Unmarshal failed. host=[%s] req=[%s] resp:[%s] err[%s]", host, string(data), string(body), err.Error())
		return err
	}

	if result.Code != 0 {
		err := fmt.Errorf("PushStrategyInvoke code is not right. host=[%s] req=[%s] resp:[%s]", host, string(data), string(body))
		logger.Error(err)
		return err
	}

	if len(result.Data.ResList) == 0 {
		err := fmt.Errorf("PushStrategyInvoke res_list is empty. host=[%s] req=[%s] resp:[%s]", host, string(data), string(body))
		logger.Error(err)
		return err
	}

	if result.Data.ResList[0].DiezhiMsgID == "" {
		err := fmt.Errorf("PushStrategyInvoke diezhi_msgid is empty. host=[%s] req=[%s] resp:[%s]", host, string(data), string(body))
		logger.Error(err)
		return err
	}

	logger.Infof("PushStrategyInvoke succeed host=[%s] req=[%s] resp=[%s]", host, string(data), string(body))

	return nil
}
