package mq

import (
	"context"
	"encoding/json"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtime"
)

type SurveyTLog struct {
	ClientID       string `json:"client_id"`
	SurveyID       string `json:"survey_id"`
	SurveyName     string `json:"survey_name"`
	STime          string `json:"stime"`
	ETime          string `json:"etime"`
	HashCode       string `json:"hash_code"`
	SurveyRecordId int32  `json:"survey_record_id"`
	VOpenID        string `json:"vopenid"`
	VRoleID        string `json:"vroleid"`
	DeviceID       string `json:"device_id"`
	IP             string `json:"ip"`
	IsValid        int32  `json:"is_valid"`
	IsDelete       int32  `json:"is_delete"`
	TimeZone       string `json:"time_zone"`
	BeginTime      string `json:"begin_time"`
	EndTime        string `json:"end_time"`
	Extra          string `json:"extra"`
	CreateTime     string `json:"ctime"`
	AnswerValid    int    `json:"answer_valid"`
	DtEventTime    string `json:"DtEventTime"`
	PartEvent      string `json:"part_event"`
	PlatId         string `json:"platid"`
	ClientIP       string `json:"ClientIP"`
}

// UploadSurvey 上报问卷TLog
// ctx: 上下文
// id: 问卷ID
// name: 问卷名称
// startTime: 开始时间
// endTime: 结束时间
// hash: 问卷的哈希值
// appId: 租户ID
func UploadSurvey(ctx context.Context, tlog *SurveyTLog) {
	if kafkaProducer == nil {
		return
	}
	tlog.PartEvent = "survey_record_new"
	tlog.DtEventTime = xtime.Now().Format(time.DateTime)
	bts, _ := json.Marshal(tlog)
	msg := xkafka.Message{
		Value: bts,
	}
	err := kafkaProducer.WriteMessages(ctx, msg)
	if err != nil {
		xlog.FromContext(ctx).Error("write kafka UploadSurvey err", xlog.Err(err))
	}
}

type SurveyRecordDetails struct {
	ClientId        string `json:"client_id"`
	SurveyId        int64  `json:"survey_id"`
	SurveyName      string `json:"survey_name"`
	HashCode        string `json:"hash_code"`
	Id              int32  `json:"id"`
	VOpenId         string `json:"vopenid"`
	VRoleId         string `json:"vroleid"`
	DeviceId        string `json:"device_id"`
	Ip              string `json:"ip"`
	SurveyRecordId  int32  `json:"survey_record_id"`
	Question        string `json:"question"` // 题目ID
	QuestionContent string `json:"question_content"`
	Option          string `json:"option"`         // 选择类题目用户选项
	OptionContent   string `json:"option_content"` // 填空类题目用户输入内容
	Text            string `json:"text"`
	StartTime       string `json:"stime"`       // 答题开始时间
	EndTime         string `json:"etime"`       // 答题结束时间
	PartEvent       string `json:"part_event"`  // 事件类型
	DtEventTime     string `json:"DtEventTime"` // 事件发生时间
	ClientIP        string `json:"ClientIP"`    // 客户端IP
}

// UploadSurveyRecordDetails 上报问卷答题详情 TLog
func UploadSurveyRecordDetails(ctx context.Context, details []*SurveyRecordDetails) {
	if kafkaProducer == nil {
		return
	}
	var msgs []xkafka.Message
	now := xtime.Now().Format(time.DateTime)
	for _, detail := range details {
		detail.PartEvent = "survey_record_detail_new"
		detail.DtEventTime = now
		bts, _ := json.Marshal(detail)
		msgs = append(msgs, xkafka.Message{
			Value: bts,
		})
	}
	err := kafkaProducer.WriteMessages(ctx, msgs...)
	if err != nil {
		xlog.FromContext(ctx).Error("write kafka UploadSurveyRecordDetails err", xlog.Err(err))
	}
}
