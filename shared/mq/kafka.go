package mq

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
	"gitlab.papegames.com/fringe/survey/config"
)

var kafkaProducer *xkafka.Writer

func Startup() error {
	var err error
	serverConfig := config.Get()
	if len(serverConfig.Sparrow.Broker.Kafka.Writer.Brokers) == 0 {
		return nil
	}
	kafkaConfig := xkafka.StdConfig()
	kafkaConfig.WriterConfig.Dialer.SASLMechanism = nil
	kafkaProducer, err = kafkaConfig.BuildWriter()
	return err
}
