package shared

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/metric"
)

var (
	MetricAward = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "survey",
		Subsystem:   "award",
		Name:        "total",
		Help:        "survey award total",
		Labels:      []string{"clientid", "type", "ret"},
		ConstLabels: metric.TargetLabels,
	})

	MetricSurveyAllReqTotal = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "survey",
		Subsystem:   "all",
		Name:        "total",
		Help:        "Number of survey all requests.",
		Labels:      []string{"clientid", "surveyid", "hash_code", "surveyname", "type"},
		ConstLabels: metric.TargetLabels,
	})

	MetricSurveyGroupAllReqTotal = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "survey",
		Subsystem:   "all",
		Name:        "group_total",
		Help:        "Number of survey all requests.",
		Labels:      []string{"clientid", "groupid", "hash_code", "groupname", "type"},
		ConstLabels: metric.TargetLabels,
	})

	MetricCallHttpAPICostTime = metric.NewSummaryVec(&metric.SummaryVecOpts{
		Namespace:   "survey",
		Subsystem:   "calltime",
		Name:        "call_http_api_cost_time_seconds",
		Help:        "call http api const time.",
		Objectives:  map[float64]float64{0.5: 0.05, 0.7: 0.03, 0.8: 0.02, 0.9: 0.01, 0.99: 0.001},
		Labels:      []string{"uri", "ret"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsErrorOccurred = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "survey",
		Subsystem:   "survey",
		Name:        "error_occurred",
		Help:        "survey error occurred total",
		Labels:      []string{"kind", "reason", "survey"},
		ConstLabels: metric.TargetLabels,
	})
)
