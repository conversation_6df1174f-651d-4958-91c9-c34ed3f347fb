package shared

import (
	"encoding/json"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xhttp"
	"gitlab.papegames.com/fringe/survey/config"
)

var verifyDialer = xhttp.NewClient()

func Verify(id, token string) error {
	conf := config.Get()
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	val := &url.Values{}
	val.Set("clientid", conf.ClientID)
	val.Set("timestamp", timestamp)
	val.Set("sig", PigeonSig(conf.ClientSecret, timestamp))
	val.Set("nid", id)
	val.Set("token", token)
	start := time.Now()
	var e error
	defer func() {
		code := 0
		if e != nil {
			code = -1
		}
		costTime := time.Since(start)
		MetricCallHttpAPICostTime.Observe(float64(costTime), conf.ClientVerify, xcast.ToString(code))
	}()
	resp, e := verifyDialer.Get(conf.ClientVerify + "?" + val.Encode())
	if e != nil {
		return e
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("verify error with status(%d)", resp.StatusCode)
	}

	var ret struct {
		Ret int `json:"ret"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&ret); err != nil {
		return err
	}
	if ret.Ret != 0 {
		return fmt.Errorf("verify error with return(%d)", ret.Ret)
	}
	return nil
}
