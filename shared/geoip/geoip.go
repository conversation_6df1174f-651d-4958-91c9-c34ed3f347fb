package geoip

import (
	"fmt"
	"sync"
	"unsafe"

	"gitlab.papegames.com/fringe/sparrow/pkg/geoip"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcron"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/atomic"
	config2 "gitlab.papegames.com/fringe/survey/config"
)

var geoipInstance atomic.Pointer // *Config

type Location = geoip.Location

func Get() *geoip.GeoIP { return (*geoip.GeoIP)(geoipInstance.Load()) }

func GetIpLocal(ip string) (*geoip.Location, error) {
	return Get().Locate(ip)
}

func reload() error {
	config := config2.Get()
	geoipNew := geoip.New(geoip.City(config.IpLocal.City), geoip.Language(config.IpLocal.Language), geoip.IPv6(false))
	if config.IpLocal.DirPath != "" {
		if lerr := geoipNew.LoadFrom(config.IpLocal.DirPath); lerr != nil {
			xlog.Error("reload ip data err", xlog.Err(lerr))
			return lerr
		}
	} else {
		if lerr := geoipNew.Load(); lerr != nil {
			xlog.Error("reload ip data err", xlog.Err(lerr))
		}
	}
	geoipInstance.Store(unsafe.Pointer(geoipNew))
	return nil
}

var (
	once    sync.Once
	crontab = xcron.New()
)

func Startup() error {
	if err := reload(); err != nil {
		return err
	}
	// 凌晨三点定时刷新ip数据包
	once.Do(func() {
		_, _ = crontab.AddFunc("0 3 * * *", func() {
			defer func() {
				err := recover()
				if err != nil {
					xlog.Error(fmt.Sprintf("reload ip data err,%v", err))
				}
			}()
			err := reload()
			xlog.Info("reload ip data success", xlog.Err(err))
		})
		crontab.Start()
	})
	return nil
}
