package database

import (
	"fmt"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var (
	sql       *xgorm.DB
	naccSql   *xgorm.DB
	nawardSql *xgorm.DB
	rdb       *xredis.Client
)

func GetRedis() *xredis.Client {
	return rdb
}

func Get() *xgorm.DB { return sql }

func GetNacc() *xgorm.DB { return naccSql }

func GetNaward() *xgorm.DB { return nawardSql }

func Startup() error {
	var err error
	sql, err = xgorm.StdConfig().Build()
	if err != nil {
		xlog.Error(fmt.Sprintf("database.Startup with error: %s", err))
		return err
	}

	xgorm.RegisterInterceptor(sql, xgorm.TraceInterceptor())

	naccSql, err = xgorm.RawConfig("sparrow.database.naccessories").Build()
	if err != nil {
		xlog.Error(fmt.Sprintf("database.Startup with error: %s", err))
		return err
	}

	nawardSql, err = xgorm.RawConfig("sparrow.database.naward").Build()
	if err != nil {
		xlog.Error(fmt.Sprintf("database.Startup with error: %s", err))
		return err
	}

	rdb, err = xredis.StdConfig().Build()
	if err != nil {
		xlog.Error(fmt.Sprintf("database.Startup with error: %s", err))
		return err
	}

	rdb.AddHook(xredis.TracingHook())

	return nil
}
