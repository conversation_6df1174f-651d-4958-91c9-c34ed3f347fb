# service
host: "internal:9080"
register: false

# verify
client_id: 1012
client_secret: "1Pzcxyz6eFlr"
client_verify: "https://api-test.papegames.com:12101/v1/verify"

# redeem
redeem_feed: "https://api-test.papegames.com:12101/v1/redeem/feed"

ip_trust: 2
award_info_ttl : "168h" # 7days
cms_survey_ttl : "5m"
client_config_ttl : "6h"
pre_award_template_ttl : "5m"

survey:
  name: "survey"
  sharding: 16
  worker: 4
  batch: 10
  resting: "5s"
  barrier: "10m"
  immediately: true

realTimeEvent:
  hiddenKey: true
  key: "finished_survey_ids"
  hiddenPeriodKey: true
  periodKey: "control_survey_ids"
  host: "http://realtime-event:8090"
  upLog: "/v1/realtime_event/event/uplog"
  inClientIds: [2008, 1030, 1061]

answerDuration:
  clientId: [2008, 1030, 1061]
  starTime: "2025-4-10 10:22:27"
  endTime: "2025-4-17 10:22:27"
  max: 1

sparrow:
  log:
    file: "./logs/survey"
    level: debug
    rotate: "daily"

  hooks:
    log:
 #     blacklist:
 #       - "/v1/survey/submit"

    origin:
      AllowedOrigins: ["*"]
      AllowedMethods: ["HEAD","GET","POST"]
      AllowedHeaders: ["*"]
      AllowCredentials: false
      MaxAge: 3600

    ulimit:
      switch: true
      paths:
        - "/v1/survey/submit"
      shard: 32
      capacity: 1000
      rate: 512
      window: "30s"

  govern:
    enable: true
    host: "internal:9081"
  watch:
    endpoints:
      - "*************:2379"

  broker:
    kafka:
      writer:
        Brokers: [ "*************:9092", "*************:9092", "*************:9092" ]
        Topic: "survey_json_prod"

  database:
    redis:
      addr: "************:6379"
      db: 10

    mysql:
      dataSource: "tds_admin_dev:2p7oMXbaEzgpsbbg@(pc-bp1w81j8101z3d8lx.mysql.polardb.rds.aliyuncs.com:3306)/survey?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 32
      maxOpenConns: 32

    naccessories:
      dataSource: "sdk_dev:5RWbtKYkf8KqipIu@(rm-bp1ui9jwtl9g8lj29.mysql.rds.aliyuncs.com:3306)/naccessories?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 32
      maxOpenConns: 32

    naward:
      dataSource: "sdk_dev:5RWbtKYkf8KqipIu@(rm-bp1ui9jwtl9g8lj29.mysql.rds.aliyuncs.com:3306)/naward?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 32
      maxOpenConns: 32
jwt:
  auth:
    alg: "HS256"
    secret: "-SUpeR-"

keys:
  1012:
    b1d9c050a5bd4a4afe536639a0b25e29
