package intergration

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"sync"
	"testing"
	"time"

	"gitlab.papegames.com/fringe/sparrow"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xhttpexpect"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/server"
	"gitlab.papegames.com/fringe/survey/service/survey"
	"gitlab.papegames.com/fringe/survey/shared"
	"gitlab.papegames.com/fringe/survey/shared/geoip"
	"gitlab.papegames.com/fringe/survey/watch"
)

var expect = xconvey.NewExpector(server.Get().GetGinEngine())

func TestMain(m *testing.M) {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		database.Startup,
		server.Startup,
		watch.Startup,
		survey.Startup,
		shared.Startup,
		geoip.Startup,
	).Server(server.Get())

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		app.WithoutRedirectStderr().Launch()
	}()

	waitReady(app)

	fmt.Println("TestMain start")

	c := m.Run()

	app.Stop()
	wg.Wait()

	fmt.Println("TestMain done")

	os.Exit(c)
}

func waitReady(app *sparrow.Application) {
	app.WaitReady(context.Background())
	address, err := getAddress(config.Get().Host)
	if err != nil {
		panic(err)
	}
	for i := 0; i < 30; i++ {
		resp, err := http.Get("http://" + address + "/v1/survey/health")
		if err == nil && resp.StatusCode == 200 {
			resp.Body.Close()
			return
		}
		time.Sleep(time.Millisecond * 100)
	}
	panic("waiting too long")
}

func requestObjectExpect(obj *xhttpexpect.Object, withoutData ...bool) {
	var keys []interface{}
	if len(withoutData) != 0 && withoutData[0] {
		keys = []interface{}{"code", "info", "request_id"}
	} else {
		keys = []interface{}{"code", "info", "request_id", "data"}
	}
	obj.Keys().ContainsOnly(keys...)
	obj.Value("code").IsEqual(0)
	obj.Value("info").IsEqual("OK")
	obj.Value("request_id").NotNull()
}

func getAddress(host string) (string, error) {
	var address string
	h, p, err := net.SplitHostPort(config.Get().Host)
	if err != nil {
		return "", err
	}
	switch h {
	case xnet.Localhost:
		address = net.JoinHostPort("127.0.0.1", p)
	case xnet.Internal:
		h, err := xnet.InternalIP()
		if err != nil {
			return "", err
		}
		address = net.JoinHostPort(h, p)
	}
	return address, nil
}
