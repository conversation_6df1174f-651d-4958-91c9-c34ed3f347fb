package intergration

import (
	"encoding/json"
	"fmt"
	"github.com/bytedance/sonic"
	"net/http"
	"net/url"
	"strconv"
	"testing"
	"time"

	"github.com/golang/snappy"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/model"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/service/survey"
	"gitlab.papegames.com/fringe/survey/shared"

	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xhttpexpect"
)

const (
	ip            = "127.0.0.1"
	xForwardedFor = "127.0.0.1,127.0.0.1,127.0.0.1,127.0.0.1,127.0.0.1"
)

var (
	clientid = "2008"
	account  = "zjhahahaha123"
	password = "zj123456"

	openid = "2242562"
	roleId = "100002"

	redeemHead = "nRVD"
	platform   = 1
	zoneid     = 1
	surveyId   = "bwy4p1j5l97" // 测试问卷
	deviceId   = "123-456-789-111"
)

func TestSurvey(t *testing.T) {
	db := database.Get()
	dbna := database.GetNaward()
	token, err := pigeonLogin(account, password)
	if err != nil {
		t.Fatal(err)
	}

	cs, err := model.GetCmsSurvey(surveyId)
	if err != nil {
		t.Fatal(err)
	}

	cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeOpenid
	cs.SettingStruct.GiftConfig.IsGiveOutByCms = true
	cs.SettingStruct.GiftConfig.GiveOutType = model.GiveOutTypeEmail
	cs.SettingStruct.GiftConfig.PreAwardConfig = &proto.CmsSurveyVersion_PreAwardConfig{Id: "60"}
	cs.SettingStruct.GiftConfig.RedeemConfig = &proto.CmsSurveyVersion_RedeemConfig{
		RedeemHead: redeemHead,
	}
	cs.SettingStruct.BaseRuleConfig.AnswerTimesConfig = &proto.CmsSurveyVersion_AnswerTimesConfig{
		LimitType: 1,
		Times:     1,
	}
	cs.SettingStruct.ZoneIds = nil

	xconvey.Convey("survey", t, func(c xconvey.C) {
		e := expect.Build(c)

		xconvey.Convey("by openid,email award", func() {
			deleteDataByOpenid(c, db, surveyId, openid)
			deleteEmailAward(c, dbna, openid, cs.Clientid)
			cs.SettingStruct.GiftConfig.GiveOutType = model.GiveOutTypeEmail
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeOpenid
			authority := signin(e, openid, token, "", surveyId)
			info := buildInfo(surveyId, openid, "", deviceId)

			submit(e, info, authority, model.GiveOutTypeEmail, true)
			expectDataByOpenid(c, db, surveyId, openid, 1, 4)
			expectEmailAward(c, dbna, openid, cs.Clientid, 0, 0)
			expectSurveyInfo(e, info, authority)
		})

		xconvey.Convey("by openid,redeem award", func() {
			time.Sleep(time.Second)
			deleteDataByOpenid(c, db, surveyId, openid)
			cs.SettingStruct.GiftConfig.GiveOutType = model.GiveOutTypeRedeem
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeOpenid
			authority := signin(e, openid, token, "", surveyId)
			info := buildInfo(surveyId, openid, "", deviceId)

			submit(e, info, authority, model.GiveOutTypeRedeem, true)
			expectDataByOpenid(c, db, surveyId, openid, 1, 4)
			expectSurveyInfo(e, info, authority)

			// answered
			signinFail(e, openid, token, "", surveyId, shared.EcodeSurveyAnswered, model.GiveOutTypeRedeem, true)
			time.Sleep(time.Second)
			submitFail(e, info, authority, shared.EcodeSurveyAnswered, model.GiveOutTypeRedeem, true)
		})

		xconvey.Convey("by role_id,email award", func() {
			deleteDataByRoleId(c, db, surveyId, roleId)
			deleteEmailAward(c, dbna, openid, cs.Clientid)
			cs.SettingStruct.GiftConfig.GiveOutType = model.GiveOutTypeEmail
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeRoleid

			authority := signin(e, openid, token, roleId, surveyId)
			info := buildInfo(surveyId, openid, roleId, deviceId)

			submit(e, info, authority, model.GiveOutTypeEmail, true)
			expectDataByRoleId(c, db, surveyId, roleId, 1, 4)

			expectEmailAward(c, dbna, openid, cs.Clientid, platform, zoneid)
			expectSurveyInfo(e, info, authority)

			signinFail(e, openid, token, roleId, surveyId, shared.EcodeSurveyAnswered, model.GiveOutTypeEmail, true)
			time.Sleep(time.Second)
			submitFail(e, info, authority, shared.EcodeSurveyAnswered, model.GiveOutTypeEmail, true)
		})

		xconvey.Convey("by role_id,redeem award", func() {
			time.Sleep(time.Second)
			deleteDataByRoleId(c, db, surveyId, roleId)
			cs.SettingStruct.GiftConfig.GiveOutType = model.GiveOutTypeRedeem
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeRoleid

			authority := signin(e, openid, token, roleId, surveyId)
			info := buildInfo(surveyId, openid, roleId, deviceId)

			submit(e, info, authority, model.GiveOutTypeEmail, true)
			expectDataByRoleId(c, db, surveyId, roleId, 1, 4)
			expectSurveyInfo(e, info, authority)

			signinFail(e, openid, token, roleId, surveyId, shared.EcodeSurveyAnswered, model.GiveOutTypeRedeem, true)
			time.Sleep(time.Second)
			submitFail(e, info, authority, shared.EcodeSurveyAnswered, model.GiveOutTypeRedeem, true)
		})

		xconvey.Convey("auth fail", func() {
			cs.SettingStruct.GiftConfig.GiveOutType = model.GiveOutTypeRedeem
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeRoleid
			info := buildInfo(surveyId, openid, roleId, deviceId)
			b, _ := json.Marshal(info)
			b = snappy.Encode(nil, b)
			obj := e.POST("/v1/survey/submit").
				WithQuery("survey_id", surveyId).
				WithBytes(b).
				WithHeader(xnet.Authority, "xxxx").
				Expect().Status(http.StatusOK).JSON().Object()
			obj.Value("code").IsEqual(shared.EcodeTokenVerifyFailed)
		})

		xconvey.Convey("by deviceid", func() {
			deleteDataByDeviceId(c, db, surveyId, deviceId)
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeNo
			cs.SettingStruct.AnswerLimitConfig.LimitType = model.LimitTypeDevice
			info := buildInfo(surveyId, "", "", deviceId)

			submit(e, info, "", "", false)
			expectDataByDeviceId(c, db, surveyId, deviceId, 1, 4)
			expectSurveyInfo(e, info, "")

			time.Sleep(time.Second)
			submitFail(e, info, "", shared.EcodeSurveyLimited, "", false)
		})

		xconvey.Convey("by ip", func() {
			deleteDataByIp(c, db, surveyId, ip)
			cs.SettingStruct.GiftConfig.GiveOutType = model.LoginTypeNo
			cs.SettingStruct.AnswerLimitConfig.LimitType = model.LimitTypeIp
			info := buildInfo(surveyId, "", "", deviceId)

			submit(e, info, "", "", false)
			expectDataByIp(c, db, surveyId, ip, 1, 4)
			expectSurveyInfo(e, info, "")

			time.Sleep(time.Second)
			submitFail(e, info, "", shared.EcodeSurveyLimited, "", false)
		})

		xconvey.Convey("survey over", func() {
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeOpenid
			cs.Etime = xtype.NewTimestamp(time.Now().Add(-5 * time.Minute))
			signinFail(e, openid, token, "", surveyId, shared.EcodeSurveyOver, "", false)
			submitFail(e, buildInfo(surveyId, openid, "", deviceId), "", shared.EcodeSurveyOver, "", false)
			cs.Etime = xtype.NewTimestamp(time.Unix(0, 0))
		})

		xconvey.Convey("survey not started", func() {
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeOpenid
			cs.Stime = xtype.NewTimestamp(time.Now().Add(5 * time.Minute))
			signinFail(e, openid, token, "", surveyId, shared.EcodeSurveyNotStarted, "", false)
			submitFail(e, buildInfo(surveyId, openid, "", deviceId), "", shared.EcodeSurveyNotStarted, "", false)
			cs.Stime = xtype.NewTimestamp(time.Unix(0, 0))
		})

		xconvey.Convey("survey paused", func() {
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeOpenid
			cs.IsPause = 1
			signinFail(e, openid, token, "", surveyId, shared.EcodeSurveyPaused, "", false)
			submitFail(e, buildInfo(surveyId, openid, "", deviceId), "", shared.EcodeSurveyPaused, "", false)
			cs.IsPause = 0
		})

		xconvey.Convey("survey delete", func() {
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeOpenid
			cs.IsDelete = 1
			signinFail(e, openid, token, "", surveyId, shared.EcodeSurveyDeleted, "", false)
			submitFail(e, buildInfo(surveyId, openid, "", deviceId), "", shared.EcodeSurveyDeleted, "", false)
			cs.IsDelete = 0
		})

		xconvey.Convey("survey not existed", func() {
			signinFail(e, openid, token, "", "ffffffffff", shared.EcodeSurveyNotExisted, "", false)
			submitFail(e, buildInfo("ffffffffff", openid, "", deviceId), "", shared.EcodeSurveyNotExisted, "", false)
		})

		// 区服校验
		xconvey.Convey("by roleid, check zoneid", func() {
			time.Sleep(time.Second)

			cs.SettingStruct.GiftConfig.GiveOutType = model.GiveOutTypeEmail
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeRoleid
			cs.SettingStruct.ZoneIds = []int32{1, 2}
			deleteDataByOpenid(c, db, surveyId, openid)
			deleteEmailAward(c, dbna, openid, cs.Clientid)
			authority := signin(e, openid, token, roleId, surveyId)
			info := buildInfo(surveyId, openid, roleId, deviceId)
			submit(e, info, authority, model.GiveOutTypeEmail, true)

			expectDataByRoleId(c, db, surveyId, roleId, 1, 4)
			expectEmailAward(c, dbna, openid, cs.Clientid, platform, zoneid)
			expectSurveyInfo(e, info, authority)

			signinFail(e, openid, token, roleId, surveyId, shared.EcodeSurveyAnswered, model.GiveOutTypeEmail, true)
			time.Sleep(time.Second)
			submitFail(e, info, authority, shared.EcodeSurveyAnswered, model.GiveOutTypeEmail, true)
		})

		// 区服校验
		xconvey.Convey("by roleid, check zoneid invalid", func() {
			time.Sleep(time.Second)
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeRoleid
			cs.SettingStruct.ZoneIds = []int32{3, 4}
			deleteDataByOpenid(c, db, surveyId, openid)
			deleteEmailAward(c, dbna, openid, cs.Clientid)
			authority := signin(e, openid, token, roleId, surveyId)
			info := buildInfo(surveyId, openid, roleId, deviceId)
			submit(e, info, authority, model.GiveOutTypeEmail, false)

			expectDataByRoleId(c, db, surveyId, roleId, 1, 4, 1)
			expectEmailAward(c, dbna, openid, cs.Clientid, platform, zoneid, 0)

			signinFail(e, openid, token, roleId, surveyId, shared.EcodeSurveyAnswered, model.GiveOutTypeEmail, false)
			time.Sleep(time.Second)
			submitFail(e, info, authority, shared.EcodeSurveyAnswered, model.GiveOutTypeEmail, false)
		})

		// 多次答题
		xconvey.Convey("survey answer multiple times", func() {
			cs.SettingStruct.BaseRuleConfig.AnswerTimesConfig = &proto.CmsSurveyVersion_AnswerTimesConfig{
				LimitType: 2,
				Times:     3,
			}
			cs.SettingStruct.GiftConfig.GiveOutType = model.GiveOutTypeEmail
			cs.SettingStruct.BaseRuleConfig.LoginType = model.LoginTypeOpenid

			deleteDataByOpenid(c, db, surveyId, openid)
			deleteEmailAward(c, dbna, openid, cs.Clientid)
			authority := signin(e, openid, token, "", surveyId)
			info := buildInfo(surveyId, openid, "", deviceId)

			submit(e, info, authority, model.GiveOutTypeEmail, true)
			submitFail(e, info, authority, shared.EcodeSurveySubmitTooFast, model.GiveOutTypeEmail, true)
			time.Sleep(time.Second)
			submit(e, info, authority, model.GiveOutTypeEmail, true)
			time.Sleep(time.Second)
			submit(e, info, authority, model.GiveOutTypeEmail, true)

			expectDataByOpenid(c, db, surveyId, openid, 3, 12)
			expectEmailAward(c, dbna, openid, cs.Clientid, 0, 0, 3)
			expectSurveyInfo(e, info, authority)
		})
	})
}

func signin(e *xhttpexpect.Expect, openid, token, roleid, surveyId string) string {
	obj := e.POST("/v1/survey/signin").WithQueryObject(map[string]interface{}{
		"openid":    openid,
		"token":     token,
		"role_id":   roleid,
		"survey_id": surveyId,
		"device_id": deviceId,
	}).
		WithHeader(xnet.Forwarded, xForwardedFor).
		Expect().Status(http.StatusOK).JSON().Object()
	requestObjectExpect(obj)
	return obj.Value("data").Object().Value("authority").String().Raw()
}

func submit(e *xhttpexpect.Expect, info *model.SurveyInfo, authority string, award string, checkSign bool) {
	b, _ := json.Marshal(info)
	b = snappy.Encode(nil, b)
	obj := e.POST("/v1/survey/submit").
		WithQuery("survey_id", info.SurveyId).
		WithBytes(b).
		WithHeader(xnet.Authority, authority).
		WithHeader(xnet.Forwarded, xForwardedFor).
		Expect().Status(http.StatusOK).JSON().Object()
	requestObjectExpect(obj)
	if award == model.GiveOutTypeRedeem {
		obj.Value("data").Object().Value("award_info").Object().Value("value").String().Match(redeemHead + `.+`)
	}

	if checkSign {
		srid := int32(obj.Value("data").Object().Value("award_info").Object().Value("survey_record_id").Number().Raw())
		sign := survey.Sign(clientid, surveyId, int32(srid), info.Openid, info.RoleId)
		obj.Value("data").Object().Value("sign").IsEqual(sign)
	}
}

func expectSurveyInfo(e *xhttpexpect.Expect, info *model.SurveyInfo, authority string) {
	obj := e.GET("/v1/survey/answer").
		WithQuery("survey_id", info.SurveyId).
		WithQuery("openid", info.Openid).
		WithQuery("role_id", info.RoleId).
		WithQuery("device_id", info.DeviceId).
		WithHeader(xnet.Authority, authority).
		WithHeader(xnet.Forwarded, xForwardedFor).
		Expect().Status(http.StatusOK).JSON().Object()
	requestObjectExpect(obj)
	resRecords := obj.Value("data").Object().Value("survey_records").Object()
	resRecords.Value("hello world").Array().Length().IsEqual(2)
	resRecords.Value("hello world").Array().Value(0).Object().Value("option").IsEqual("A1")
	resRecords.Value("hello world").Array().Value(1).Object().Value("option").IsEqual("B1")
	resRecords.Value("hello world2").Array().Length().IsEqual(2)
	resRecords.Value("hello world2").Array().Value(0).Object().Value("option").IsEqual("A2")
	resRecords.Value("hello world2").Array().Value(1).Object().Value("option").IsEqual("B2")
}

func signinFail(e *xhttpexpect.Expect, openid, token, roleId, surveyId string, code ecode.Code, award string, checkSign bool) {
	obj := e.POST("/v1/survey/signin").WithQueryObject(map[string]interface{}{
		"openid":    openid,
		"token":     token,
		"role_id":   roleId,
		"device_id": deviceId,
		"survey_id": surveyId,
	}).Expect().Status(http.StatusOK).JSON().Object()
	obj.Value("code").IsEqual(code.Value())
	if code.Value() != shared.EcodeSurveyAnswered.Value() {
		return
	}
	if award == model.GiveOutTypeRedeem {
		obj.Value("data").Object().Value("award_info").Object().Value("value").String().Match(redeemHead + `.+`)
	}
	srid := int32(obj.Value("data").Object().Value("award_info").Object().Value("survey_record_id").Number().Raw())
	if checkSign {
		sign := survey.Sign(clientid, surveyId, srid, openid, roleId)
		obj.Value("data").Object().Value("sign").IsEqual(sign)
	}
}

func submitFail(e *xhttpexpect.Expect, info *model.SurveyInfo, authority string, code ecode.Code, award string, checkSign bool) {
	b, _ := json.Marshal(info)
	b = snappy.Encode(nil, b)
	obj := e.POST("/v1/survey/submit").
		WithQuery("survey_id", info.SurveyId).
		WithBytes(b).
		WithHeader(xnet.Authority, authority).
		WithHeader(xnet.Forwarded, xForwardedFor).
		Expect().Status(http.StatusOK).JSON().Object()
	obj.Value("code").IsEqual(code.Value())
	if code.Value() != shared.EcodeSurveyAnswered.Value() {
		return
	}
	if award == model.GiveOutTypeRedeem {
		obj.Value("data").Object().Value("award_info").Object().Value("value").String().Match(redeemHead + `.+`)
	}
	srid := int32(obj.Value("data").Object().Value("award_info").Object().Value("survey_record_id").Number().Raw())

	if checkSign {
		sign := survey.Sign(clientid, surveyId, srid, info.Openid, info.RoleId)
		obj.Value("data").Object().Value("sign").IsEqual(sign)
	}
}

func pigeonLogin(account string, password string) (string, error) {
	conf := config.Get()
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	val := &url.Values{}
	val.Set("clientid", conf.ClientID)
	val.Set("timestamp", timestamp)
	val.Set("sig", shared.PigeonSig(conf.ClientSecret, timestamp))
	val.Set("account", account)
	val.Set("password", password)
	res, err := http.Get("https://api-test.papegames.com:12101/v1/login?" + val.Encode())
	if err != nil {
		return "", err
	}
	defer res.Body.Close()
	var ret struct {
		Ret   int
		Token string
	}
	if err := json.NewDecoder(res.Body).Decode(&ret); err != nil {
		return "", err
	}

	if ret.Ret != 0 {
		return "", fmt.Errorf("login ret(%v)", ret.Ret)
	}
	return ret.Token, nil
}

func buildInfo(surveyId, openid, roleId, deviceId string) *model.SurveyInfo {
	return &model.SurveyInfo{
		SurveySelector: model.SurveySelector{
			SurveyId: surveyId,
			Openid:   openid,
			RoleId:   roleId,
			DeviceId: deviceId,
		},
		SurveyRecords: map[string][]model.SurveyRecordDetail{
			"hello world": {
				{Option: "A1", Text: "yes"},
				{Option: "B1", Text: "no"},
			},
			"hello world2": {
				{Option: "A2", Text: "1"},
				{Option: "B2", Text: "2"},
			},
		},
		BeginTime: time.Now().Add(-5 * time.Minute),
		EndTime:   time.Now(),
	}
}

func expectDataByOpenid(c xconvey.C, db *xgorm.DB, surveyId, openid string, record, detail int) {
	ids := getSurveyRecordsIds(c, db, surveyId, "openid = ?", openid)
	c.So(len(ids), xconvey.ShouldEqual, record)

	var count int64
	err := db.Table(`survey_record_detail_`+surveyId).Where(`survey_record_id in (?)`, ids).Count(&count).Error
	c.So(err, xconvey.ShouldBeNil)
}

func expectDataByRoleId(c xconvey.C, db *xgorm.DB, surveyId, roleId string, record, detail int, isValid ...int) {
	var ids []int
	if len(isValid) > 0 {
		fmt.Println("role_id = ? and is_valid = " + strconv.Itoa(isValid[0]))
		ids = getSurveyRecordsIds(c, db, surveyId, "role_id = ? and is_valid = "+strconv.Itoa(isValid[0]), roleId)
		c.So(len(ids), xconvey.ShouldEqual, record)
	} else {
		ids = getSurveyRecordsIds(c, db, surveyId, "role_id = ?", roleId)
		c.So(len(ids), xconvey.ShouldEqual, record)
	}

	var count int64
	err := db.Table(`survey_record_detail_`+surveyId).Where(`survey_record_id in (?)`, ids).Count(&count).Error
	c.So(err, xconvey.ShouldBeNil)
	c.So(count, xconvey.ShouldEqual, detail)
}

func expectDataByDeviceId(c xconvey.C, db *xgorm.DB, surveyId, deviceId string, record, detail int) {
	ids := getSurveyRecordsIds(c, db, surveyId, "device_id = ?", deviceId)
	c.So(len(ids), xconvey.ShouldEqual, record)

	var count int64
	err := db.Table(`survey_record_detail_`+surveyId).Where(`survey_record_id in (?)`, ids).Count(&count).Error
	c.So(err, xconvey.ShouldBeNil)
	c.So(count, xconvey.ShouldEqual, detail)
}

func expectDataByIp(c xconvey.C, db *xgorm.DB, surveyId, ip string, record, detail int) {
	ids := getSurveyRecordsIds(c, db, surveyId, "ip = ?", ip)
	c.So(len(ids), xconvey.ShouldEqual, record)

	var count int64
	err := db.Table(`survey_record_detail_`+surveyId).Where(`survey_record_id in (?)`, ids).Count(&count).Error
	c.So(err, xconvey.ShouldBeNil)
	c.So(count, xconvey.ShouldEqual, detail)
}

func expectEmailAward(c xconvey.C, db *xgorm.DB, openid, clientid string, platform int, zoneid int, times ...int64) {
	var count int64
	num := int64(1)
	if len(times) > 0 {
		num = times[0]
	}
	err := db.Table(`pre_award_v2`).Where(`openid = ? and clientid = ? and platform = ? and zoneid = ?`, openid, clientid, platform, zoneid).Count(&count).Error
	c.So(err, xconvey.ShouldBeNil)
	xconvey.So(count, xconvey.ShouldEqual, num)
}

func deleteEmailAward(c xconvey.C, db *xgorm.DB, openid, clientid string) {
	err := db.Exec(`delete from pre_award_v2 where openid = ? and clientid = ? `, openid, clientid).Error
	c.So(err, xconvey.ShouldBeNil)
}

func deleteDataByOpenid(c xconvey.C, db *xgorm.DB, surveyId, openid string) {
	ids := getSurveyRecordsIds(c, db, surveyId, "openid = ?", openid)
	deleteData(c, db, surveyId, ids)
}

func deleteDataByRoleId(c xconvey.C, db *xgorm.DB, surveyId, roleId string) {
	ids := getSurveyRecordsIds(c, db, surveyId, "role_id = ?", roleId)
	deleteData(c, db, surveyId, ids)
}

func deleteDataByDeviceId(c xconvey.C, db *xgorm.DB, surveyId, deviceId string) {
	ids := getSurveyRecordsIds(c, db, surveyId, "device_id = ?", deviceId)
	deleteData(c, db, surveyId, ids)
}

func deleteDataByIp(c xconvey.C, db *xgorm.DB, surveyId, ip string) {
	ids := getSurveyRecordsIds(c, db, surveyId, "ip = ?", ip)
	deleteData(c, db, surveyId, ids)
}

func deleteData(c xconvey.C, db *xgorm.DB, surveyId string, ids []int) {
	err := db.Exec(`delete from survey_record_`+surveyId+` where id in (?)`, ids).Error
	c.So(err, xconvey.ShouldBeNil)
	err = db.Exec(`delete from survey_record_detail_`+surveyId+` where survey_record_id in (?)`, ids).Error
	c.So(err, xconvey.ShouldBeNil)
}

func getSurveyRecordsIds(c xconvey.C, db *xgorm.DB, surveyId string, query string, value interface{}) []int {
	ids := make([]int, 0)
	err := db.Table(`survey_record_`+surveyId).Where(query, value).Pluck(`id`, &ids).Error
	c.So(err, xconvey.ShouldBeNil)
	return ids
}

func TestSubmit(t *testing.T) {
	xconvey.Convey("TestSubmit", t, func(c xconvey.C) {
		e := expect.Build(c)
		token, err := pigeonLogin(account, password)
		if err != nil {
			t.Fatal(err)
		}
		authority := signin(e, openid, token, roleId, surveyId)
		info := buildInfo(surveyId, openid, roleId, deviceId)

		submit(e, info, authority, model.GiveOutTypeEmail, true)

	})
}

func TestSignCustom(t *testing.T) {
	xconvey.Convey("TestSubmitCustom", t, func(c xconvey.C) {
		surveyId = "2f44p1j5l97" // 1681
		e := expect.Build(c)
		token, err := pigeonLogin(account, password)
		if err != nil {
			t.Fatal(err)
		}
		authority := signin(e, openid, token, roleId, surveyId)
		t.Log(authority)
	})
}

func TestSubmitCustom(t *testing.T) {
	xconvey.Convey("TestSubmitCustom", t, func(c xconvey.C) {
		surveyId = "2f44p1j5l97" // 1681

		e := expect.Build(c)
		token, err := pigeonLogin(account, password)
		if err != nil {
			t.Fatal(err)
		}
		authority := signin(e, openid, token, roleId, surveyId)

		// 答案
		answer := `{"id":0,"survey_records":{"u7pYbbdzZm":[{"option":"bUZVfG5RYH","text":"brea"}]},"begin_time":"2025-03-11T03:42:00.245Z","end_time":"2025-03-11T03:42:55.321Z","survey_id":"2f44p1j5l97","openid":"2242562","role_id":"100002","survey_answer_times":0,"device_id":"Z6PD4INoFGUx7VqzL4UtDkjY9AF858-w","extra":"","falg1":0}`
		info := new(model.SurveyInfo)
		err = sonic.UnmarshalString(answer, info)
		if err != nil {
			t.Fatal(err)
		}

		// 提交
		b, _ := json.Marshal(info)
		b = snappy.Encode(nil, b)
		obj := e.POST("/v1/survey/submit").
			WithQuery("survey_id", info.SurveyId).
			WithBytes(b).
			WithHeader(xnet.Authority, authority).
			WithHeader(xnet.Forwarded, xForwardedFor).
			Expect().Status(http.StatusOK).JSON().Object()

		requestObjectExpect(obj)
	})
}
