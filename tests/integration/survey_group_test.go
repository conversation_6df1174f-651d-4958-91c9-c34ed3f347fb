package intergration

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/golang/snappy"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
)

func TestGroupConfig(t *testing.T) {
	<PERSON>vey("TestGroupConfig", t, func(c xconvey.C) {
		r := expect.Build(c).
			GET("/v1/survey/group/config").
			WithQuery("group", "gs_jxtbsayv6d311").
			WithQuery("lang", "zh-cn").
			Expect().
			Status(http.StatusOK).
			JSON().Object()
		requestObjectExpect(r)
		data := r.Value("data").Object()
		data.Value("role").NotNull()
	})
}

func TestCmsSurveySubmit(t *testing.T) {
	token, err := pigeonLogin(account, password)
	if err != nil {
		t.Fatal(err)
	}

	<PERSON>vey("TestSubmit", t, func(c xconvey.C) {
		surveyId := "i2kklb8dy4p"
		groupId := "gs_jxtbsayv6d3"
		roleId = "roleid"
		obj := expect.Build(c).POST("/v1/survey/signin").WithQueryObject(map[string]interface{}{
			"openid":    openid,
			"token":     token,
			"role_id":   "roleid",
			"survey_id": surveyId,
			"device_id": deviceId,
			"group":     groupId,
		}).WithHeader(xnet.Forwarded, xForwardedFor).
			Expect().Status(http.StatusOK).JSON().Object()
		requestObjectExpect(obj)
		authority := obj.Value("data").Object().Value("authority").String().Raw()

		info := buildInfo(surveyId, openid, roleId, deviceId)
		b, _ := json.Marshal(info)
		b = snappy.Encode(nil, b)
		obj = expect.Build(c).POST("/v1/survey/submit").
			WithQuery("survey_id", surveyId).
			WithQuery("group", groupId).
			WithBytes(b).
			WithHeader(xnet.Authority, authority).
			WithHeader(xnet.Forwarded, xForwardedFor).
			Expect().Status(http.StatusOK).JSON().Object()
		requestObjectExpect(obj)
	})
}
