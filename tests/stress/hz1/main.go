package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/golang/snappy"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
	"gitlab.papegames.com/fringe/survey/shared"
)

var (
	client = xresty.New()
	//survey-graph-pre.infoldgames.com
	//survey-graph-stress.papegames.com
	baseUrl  = "https://survey-graph-stress.papegames.com"
	account  = "*********"
	password = "zj123456"
	clientId = "1012"
	roleId   = "***************"
	surveyId = "infklb8dy4p"

	cipher, err = jwt.NewToken(
		"HS256",
		"-SUpeR-",
	)
)

func main() {
	// token, err := pigeonLogin(account, "password")
	// if err != nil {
	// 	panic(err)
	// }
	// fmt.Println(token)

	// token := signin(baseUrl, "*********", "203416b65e9023c2b8be77ff089ded4abffc1e08h", "1111111", "dkr1z5d28qn", "1111dddddd")

	// fmt.Println(token)

	token := genJWt(account)

	submit(buildInfo(surveyId, account, roleId, "device_xxx", json.RawMessage(data)), token)

}

func submit(info *SurveyInfo, authority string) {
	b, _ := json.Marshal(info)
	b = snappy.Encode(nil, b)
	r, err := client.R().SetHeader(xnet.Authority, authority).
		SetQueryParam("survey_id", info.SurveyId).
		SetBody(b).
		Post(baseUrl + "/v1/survey/submit")
	if err != nil {
		panic(err)
	}
	fmt.Println(string(r.Body()))
}

func signin(baseUsrl string, openid, token, roleid, surveyId, deviceId string) string {
	r, err := client.R().SetQueryParams(map[string]string{
		"openid":    openid,
		"token":     token,
		"role_id":   roleid,
		"survey_id": surveyId,
		"device_id": deviceId,
	}).Post(baseUrl + "/v1/survey/signin")
	if err != nil {
		panic(err)
	}
	fmt.Println(string(r.Body()))
	return ""
}

func pigeonLogin(account string, password string) (string, error) {
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	val := &url.Values{}
	val.Set("clientid", clientId)
	val.Set("timestamp", timestamp)
	val.Set("sig", shared.PigeonSig("1Pzcxyz6eFlr", timestamp))
	val.Set("account", account)
	val.Set("password", password)
	res, err := http.Get("https://api-test.papegames.com:12101/v1/login?" + val.Encode())
	if err != nil {
		return "", err
	}
	defer res.Body.Close()
	var ret struct {
		Ret   int
		Token string
	}
	if err := json.NewDecoder(res.Body).Decode(&ret); err != nil {
		return "", err
	}

	if ret.Ret != 0 {
		return "", fmt.Errorf("login ret(%v)", ret.Ret)
	}
	return ret.Token, nil
}

var data = `{"0-n-Dmy2Id":[{"option":"wMErqOw5vN","text":""}],"0ZsCBZS4Xn":[{"option":"xJnyuoW2Yx","text":""}],"0s45UM4jIm":[{"option":"-IDyBEzr24","text":""}],"0yIPZ3-oM9":[{"option":"ZcNkvcf62a","text":""}],"1KfN1GXdtJ":[{"option":"S1VLaXhVL_","text":""}],"1nwHfjlt1g":[{"option":"ZcNkvcf62a","text":""}],"2R_oJJ_s_L":[{"option":"sr95WkFlG4","text":""}],"2aCjhwbcrH":[{"option":"F2-EK5CD4F","text":""}],"2kzFwMIjFK":[{"option":"GCv5Q8nu52","text":""}],"3t8YYZ--ju":[{"option":"AQL6MQm-zZ","text":""}],"3wHwVyyT-z":[{"option":"","text":"都好。别搞v那种虚拟偶像、主播就行，对那个圈子很反感。自成一派吧哈哈。不想看官方出暖暖cos，暖暖就是暖暖，无法被扮演。电影赶紧端上来吧"}],"4J_4KOZJ0l":[{"option":"Dljq5s6oFd","text":""}],"54JIL4luqE":[{"option":"Asw13H5SVy","text":""}],"5X-O6KdywH":[{"option":"N0ufe4P_2g","text":""}],"6YhmQ6B13q":[{"option":"jH7iW7Qlmv","text":""}],"7H_C2Pw5qz":[{"option":"XbstyZohtB","text":""}],"7HzNS7uDge":[{"option":"OgzX6zQ8RU","text":""}],"7wstToJUZp":[{"option":"xjRo9qUOUG","text":""}],"88mmRqpW4V":[{"option":"","text":"找不到路啊。。。地图不顶用了。还犯路痴了，都不知道从哪来的，在哪里，那里有没有去过。"}],"8W1MN_CY2h":[{"option":"ZcNkvcf62a","text":""}],"8ar8RNe6yN":[{"option":"zhjXSBUMHJ","text":""}],"94dBtvw_Ur":[{"option":"sr95WkFlG4","text":""}],"95dhPBR8h3":[{"option":"Dljq5s6oFd","text":""}],"9J2dZUh8PG":[{"option":"sr95WkFlG4","text":""}],"9NmQbdmYyf":[{"option":"mdDBPZnFKC","text":""}],"AeW-LM_Q5F":[{"option":"Asw13H5SVy","text":""}],"BBd9LmHK-e":[{"option":"dFqa1BYesq","text":""}],"BLvFcVs6NP":[{"option":"DkyIyOk0VN","text":""}],"Bks1uh7gHg":[{"option":"bnvWDFPKj-","text":""}],"C8L7e8JrzK":[{"option":"Ggr1RVXJJV","text":""}],"CHe8QsGBj8":[{"option":"zhjXSBUMHJ","text":""}],"C_DjudS3_j":[{"option":"Dljq5s6oFd","text":""}],"Ck7B_zLOS4":[{"option":"vQbnLnNL49","text":""}],"CrMuyo1ACd":[{"option":"fR4bV6TQjG","text":""}],"DqnBJpKZqA":[{"option":"U8tj91VNj5","text":""}],"DsH8FlTnSR":[{"option":"Dljq5s6oFd","text":""}],"EnKVMtcdS2":[{"option":"fnCnR19zDw","text":""}],"F9HGUO7lco":[{"option":"XH7JQEEZib","text":""}],"FTl9M53EF-":[{"option":"8ZG4990qwA","text":""}],"FaOcb0U4AV":[{"option":"Dljq5s6oFd","text":""}],"HXjCr9SOK0":[{"option":"E1eTYFlnWu","text":""}],"He-f1Ywwk8":[{"option":"U1t6QT4Igg","text":""}],"HzLCggNF_I":[{"option":"nhqcRzxRWy","text":""}],"Iqty7UwFNp":[{"option":"bnvWDFPKj-","text":""}],"Iyb49IPbFT":[{"option":"bnvWDFPKj-","text":""}],"JpkP1ko1Ah":[{"option":"N0ufe4P_2g","text":""}],"LN1CDHNtl7":[{"option":"gKkgqNgahH","text":""}],"LoYJKvz1jF":[{"option":"gGegFSvHS2","text":""},{"option":"VEjvDy7Nws","text":""},{"option":"LXDcK5R3Ve","text":""},{"option":"MG7RpOg3YA","text":""},{"option":"eF2kMHWxpo","text":""}],"NIEhvt16rD":[{"option":"gKkgqNgahH","text":""}],"NIXmPE8ubS":[{"option":"ZyDNsigRtQ","text":""}],"Nz_ZOf2eTb":[{"option":"4jZ4jB5gQz","text":""},{"option":"MqxIXmkp-2","text":""}],"PDKSMCy276":[{"option":"","text":"池子里散件服饰少。\n4星不吸引人。\n5星风格调性大差不差。\n商城是2级页面吗，还要从共鸣那里跳过去，有点麻烦。\n"}],"PTBNQ-uMT-":[{"option":"n7Nqp3A6cA","text":""}],"PTLvV3hfSd":[{"option":"","text":"还是给个线路图吧"}],"Q1IYKScZYl":[{"option":"VWOz_-l7EL","text":""}],"T-s0xyEZWx":[{"option":"2fdf1VVZgb","text":""}],"TdrdPhd7C8":[{"option":"O3kOjcXbSv","text":""}],"Tu3l5wV6u1":[{"option":"VWOz_-l7EL","text":""}],"UBCRAzfOTg":[{"option":"VWOz_-l7EL","text":""}],"VEtlQWltoa":[{"option":"","text":"可爱，可爱，可爱\n目前见过的最好最喜欢的甜系女角色\n很独立很自主，很有魅力"}],"VM2bT16rCL":[{"option":"O3kOjcXbSv","text":""}],"VSWScEbnN4":[{"option":"L8nWuTo9b-","text":""}],"W0ULXaKXKC":[{"option":"8ZG4990qwA","text":""}],"W5_bqeRGeJ":[{"option":"xBbM8APsba","text":""}],"XAe9byHYuL":[{"option":"xBbM8APsba","text":""}],"YhyNvl0F12":[{"option":"O628MYVIb0","text":""}],"_W2QIu2sMt":[{"option":"nILHIuVXLr","text":""}],"aeZjzADtH5":[{"option":"F2-EK5CD4F","text":""}],"aj4wjX7hgh":[{"option":"zhjXSBUMHJ","text":""}],"cMj0jXk-5t":[{"option":"DkyIyOk0VN","text":""}],"dumRsitcAs":[{"option":"Ggr1RVXJJV","text":""}],"eIiRUB6eaW":[{"option":"","text":"表情僵硬。\n别搞黑深残的东西拜托。\n有些情节稍微有点尬了。"}],"eoVIwPMnzj":[{"option":"n7Nqp3A6cA","text":""}],"f9h71adkxN":[{"option":"n7Nqp3A6cA","text":""}],"frFHpQy4ll":[{"option":"dCFiV5mSHT","text":""}],"gvaM4cGdrp":[{"option":"OgZybeVTQf","text":""},{"option":"fWZJyj4A4g","text":""},{"option":"6NaWf1p4Ve","text":""},{"option":"eKQaoY9zwk","text":""},{"option":"IkOC7sRvTT","text":"效果并不丰富，顶替了染色好可惜"}],"h1XY6jQqJs":[{"option":"E1eTYFlnWu","text":""}],"iCrpOw4qvG":[{"option":"chRigEEA5P","text":""}],"iDDQ52M3Gw":[{"option":"AQL6MQm-zZ","text":""}],"iTQeE6gkVV":[{"option":"fnCnR19zDw","text":""}],"im2IwXfgsw":[{"option":"YYgsUlPAXk","text":""}],"j5Y15tDxBX":[{"option":"wH75yUIDrw","text":""}],"kLRcfFeXtE":[{"option":"gKkgqNgahH","text":""}],"l3r_hqeuL4":[{"option":"E1eTYFlnWu","text":""}],"lFfSE4mtis":[{"option":"bnvWDFPKj-","text":""}],"n4SrkdP4cn":[{"option":"","text":"1.云端赶紧抬上来吧！中式！古典！\n2.人物表情有点僵硬\n3.你的ui图标不能让我想起来是做什么的。\n4.功能页面有点乱，要不每个页面都给个相互跳转的链接吧。\n5，池子里的普通散件衣服有点少啊。\n6，pc端换装，能不能用鼠标滚轮或者按键跳到下一件衣服，方便每件快速浏览。不像用鼠标一个个去点。\n7.换装页面吧套装那类放底下去，排序参考一下前作好吗，有点不习惯。\n8.自行车的适用范围有点少，复活的时候能不能不要收回自行车。重新借好麻烦的。\n9.洞穴给个小地图或者路线图。\n10.传送点你在规划一下，该有的地方没有，也不知道是不是没有激活。流转柱有点不好找，\n11.那些个体商贩，也在地图上标一下吧（为了避免太乱干脆做成几类地图好了，交通指引，材料任务探索，npc地点）"}],"nv1LkysNZP":[{"option":"WtiiGnr0up","text":""}],"o_5USKl1QC":[{"option":"Qx7JMuhibp","text":""}],"pNgJ9fw0h1":[{"option":"61sJUgR8u5","text":""}],"pXDRcRZwE8":[{"option":"_5E2zP-IFR","text":""}],"rwS8EHWdXd":[{"option":"U8tj91VNj5","text":""}],"tcK55gGNV9":[{"option":"Ggr1RVXJJV","text":""}],"u6iQDfwIcg":[{"option":"yi0sezs1xN","text":""}],"v_yvQ2tu_e":[{"option":"MqxIXmkp-2","text":""},{"option":"4jZ4jB5gQz","text":""},{"option":"pmGagnmoEc","text":""},{"option":"79Q7CUsVcK","text":""}],"w3dePmuNJX":[{"option":"","text":"单品灵可爱。\n整体太暗了，感觉都是一个调调，每个石柱都是同一个石柱，我都不知道自己在哪，做点差异变换\n"}],"wX1874RzYC":[{"option":"chRigEEA5P","text":""}],"wiegZwe_Kw":[{"option":"VWOz_-l7EL","text":""}],"xHwdn8cpVQ":[{"option":"gKkgqNgahH","text":""}],"yilNOMjpiU":[{"option":"XbstyZohtB","text":""}],"zLf6KPAfGe":[{"option":"B5t44P2Hoq","text":""},{"option":"MPw6GzGsOj","text":""}],"zXkkRF8-aM":[{"option":"nILHIuVXLr","text":""}]}`

type SurveyInfo struct {
	SurveyRecords json.RawMessage `json:"survey_records" gorm:"-"`
	BeginTime     time.Time       `json:"begin_time"`
	EndTime       time.Time       `json:"end_time"`
	// 问卷ID
	SurveyId string `json:"survey_id,omitempty" gorm:"-"`
	// 账号ID
	Openid string `json:"openid,omitempty"`
	// 角色ID
	RoleId string `json:"role_id,omitempty"`
	// 设备ID
	DeviceId string `json:"device_id,omitempty"`
}

func buildInfo(surveyId, openid, roleId, deviceId string, data json.RawMessage) *SurveyInfo {
	return &SurveyInfo{
		SurveyId:      surveyId,
		Openid:        openid,
		RoleId:        roleId,
		DeviceId:      deviceId,
		SurveyRecords: data,
		BeginTime:     time.Now().Add(-5 * time.Minute),
		EndTime:       time.Now(),
	}
}

func genJWt(account string) string {
	authority, err := cipher.Sign(&jwt.Claims{
		ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
		Id:        account,
	})
	if err != nil {
		panic(err)
	}
	return authority
}
