package main

import (
	"gitlab.papegames.com/fringe/sparrow"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	_ "gitlab.papegames.com/fringe/survey/model"
	"gitlab.papegames.com/fringe/survey/server"
	"gitlab.papegames.com/fringe/survey/service/award"
	"gitlab.papegames.com/fringe/survey/service/survey"
	"gitlab.papegames.com/fringe/survey/shared"
	"gitlab.papegames.com/fringe/survey/shared/geoip"
	"gitlab.papegames.com/fringe/survey/shared/mq"
	"gitlab.papegames.com/fringe/survey/watch"
)

func main() {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		database.Startup,
		mq.Startup,
		shared.Startup,
		geoip.Startup,
		server.Startup,
		watch.Startup,
		survey.Startup,
		award.Startup,
	).Server(server.Get(), server.GetAdmin()).Launch()
}
