# service
host: ":9080"
register: false
admin_host: ":8089"

feishu_webhook:
  prefix: "【Survey】【dev】"
  webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/cb3e7584-e3d3-4e00-aa53-bb4368409eef"
  ignore: "survey answered##in.Validate with error##token verify failed##openid is empty openid"

# verify
client_id: 1003
client_secret: "jshR3bIqYUSF"
client_verify: "https://api-test.papegames.com:12101/v1/verify"

# redeem
redeem_feed: "https://api-test.papegames.com:12101/v1/redeem/feed"

ip_trust: 1
award_info_ttl : "168h" # 7days
cms_survey_ttl : "5m"
client_config_ttl : "6h"
pre_award_template_ttl : "5m"

survey:
  name: "survey"
  sharding: 2
  parallel: 2
  worker: 2
  batch: 10
  resting: "5s"
  barrier: "10m"

award:
  name: "award"
  sharding: 2
  parallel: 2
  worker: 2
  batch: 10
  resting: "5s"
  barrier: "10m"
  strategy:
    delay: "1m"
    max_delay: "2m"

sparrow:
  log:
    file: "./logs/survey"
    level: "info"
    rotate: "hourly"

  trace:
    enable: true
    async: true
    endpoint: "pt-tracing-test.diezhi.net:4317"
    ratio : 1
  hooks:
    log:
      blacklist:
        - "/v1/survey/submit"
        - "/v1/survey/config"
      Ignore:
        - "/v1/survey/health"
    origin:
      AllowedOrigins: ["*"]
      AllowedMethods: ["HEAD","GET","POST"]
      AllowedHeaders: ["*"]
      AllowCredentials: false
      MaxAge: 3600
    ulimit:
      switch: true
      paths:
        - "/v1/survey/submit"
      shard: 32
      capacity: 1000
      rate: 512
      window: "30s"
    local:
      trust: 2
  govern:
    enable: true
    host: ":9081"

  database:
    redis:
      addr: "************:6379"
      db: 10

    mysql:
      dataSource: "tds_admin_dev:2p7oMXbaEzgpsbbg@(pc-bp1w81j8101z3d8lx.mysql.polardb.rds.aliyuncs.com:3306)/survey?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 32
      maxOpenConns: 32

    naccessories:
      dataSource: "tds_admin_dev:2p7oMXbaEzgpsbbg@(pc-bp1w81j8101z3d8lx.mysql.polardb.rds.aliyuncs.com:3306)/survey?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 32
      maxOpenConns: 32

    naward:
      dataSource: "tds_admin_dev:2p7oMXbaEzgpsbbg@(pc-bp1w81j8101z3d8lx.mysql.polardb.rds.aliyuncs.com:3306)/survey?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 32
      maxOpenConns: 32

  registrar:
    prefix: "/sparrow/sd/"
    addrs:
      - "*************:2379"

  watch:
    endpoints:
      - "*************:2379"

  broker:
    kafka:
      writer:
        Brokers: ["*************:9092", "*************:9092", "10.212.35.178:9092"]
        Topic: "survey_json_prod"

jwt:
  auth:
    alg: "HS256"
    secret: "-SUpeR-"

keys:
  1051:
    6b27bd974829d3e026ecf702afc222d5
  1059:
    6b27bd974829d3e026ecf702afc222d5
  1012:
    b1d9c050a5bd4a4afe536639a0b25e29

push:
  strategy_invoke: "http://push-api-inner-test.diezhi.net/v1/push/strategy/invoke"