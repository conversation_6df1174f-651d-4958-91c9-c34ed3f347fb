// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HealthRequest) Reset() {
	*x = HealthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthRequest) ProtoMessage() {}

func (x *HealthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthRequest.ProtoReflect.Descriptor instead.
func (*HealthRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{0}
}

// Request message for SurveyService.SubmitSurvey
type SubmitSurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷ID
	SurveyId string `protobuf:"bytes,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	// 问卷内容(经snappy压缩),eg.
	//
	//	{
	//		"survey_id": "xxxxx",
	//	  "openid" : "",
	//	  "role_id" : "",
	//	  "device_id" : "",
	//		"begin_time": "2022-01-02T09:13:47+08:00",
	//		"end_time": "2022-01-02T09:13:47+08:00",
	//		"survey_records": {
	//			"hello world": [{
	//				"option": "A",
	//				"text": "yes"
	//			}]
	//		},
	//		"extra": "xxxx",
	//	  "flag": 1
	//	}
	Body []byte `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	// 时区
	Timezone string `protobuf:"bytes,3,opt,name=timezone,proto3" json:"timezone,omitempty"`
	// 问卷组
	Group string `protobuf:"bytes,4,opt,name=group,proto3" json:"group,omitempty"`
	// 渠道id
	Platid string `protobuf:"bytes,5,opt,name=platid,proto3" json:"platid,omitempty"`
}

func (x *SubmitSurveyRequest) Reset() {
	*x = SubmitSurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitSurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitSurveyRequest) ProtoMessage() {}

func (x *SubmitSurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitSurveyRequest.ProtoReflect.Descriptor instead.
func (*SubmitSurveyRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{1}
}

func (x *SubmitSurveyRequest) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

func (x *SubmitSurveyRequest) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *SubmitSurveyRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *SubmitSurveyRequest) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *SubmitSurveyRequest) GetPlatid() string {
	if x != nil {
		return x.Platid
	}
	return ""
}

// Response message for SurveyService.SubmitSurvey
type SubmitSurveyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发奖信息
	AwardInfo *AwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	// 签名字段
	Sign string `protobuf:"bytes,2,opt,name=sign,proto3" json:"sign,omitempty"`
}

func (x *SubmitSurveyResponse) Reset() {
	*x = SubmitSurveyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitSurveyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitSurveyResponse) ProtoMessage() {}

func (x *SubmitSurveyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitSurveyResponse.ProtoReflect.Descriptor instead.
func (*SubmitSurveyResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{2}
}

func (x *SubmitSurveyResponse) GetAwardInfo() *AwardInfo {
	if x != nil {
		return x.AwardInfo
	}
	return nil
}

func (x *SubmitSurveyResponse) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

// 发奖信息
type AwardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 形式, 0: 邮件 1: 兑换码
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// 值,形式为兑换码时表示兑换码
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// 问卷记录ID
	SurveyRecordId int32 `protobuf:"varint,3,opt,name=survey_record_id,json=surveyRecordId,proto3" json:"survey_record_id,omitempty"`
}

func (x *AwardInfo) Reset() {
	*x = AwardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AwardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AwardInfo) ProtoMessage() {}

func (x *AwardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AwardInfo.ProtoReflect.Descriptor instead.
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{3}
}

func (x *AwardInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AwardInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *AwardInfo) GetSurveyRecordId() int32 {
	if x != nil {
		return x.SurveyRecordId
	}
	return 0
}

type AnswerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷ID
	SurveyId string `protobuf:"bytes,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	// 用户ID
	Openid string `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid"`
	// 角色ID
	RoleId string `protobuf:"bytes,3,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// 设备ID
	DeviceId string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	Lang     string `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *AnswerRequest) Reset() {
	*x = AnswerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnswerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnswerRequest) ProtoMessage() {}

func (x *AnswerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnswerRequest.ProtoReflect.Descriptor instead.
func (*AnswerRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{4}
}

func (x *AnswerRequest) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

func (x *AnswerRequest) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *AnswerRequest) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *AnswerRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *AnswerRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

// Response message for SurveyService.Answer
type AnswerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷答题信息
	//
	//	{
	//			"hello world": [{
	//				"option": "A1",
	//				"text": "yes"
	//			}, {
	//				"option": "B1",
	//				"text": "no"
	//			}],
	//			"hello world2": [{
	//				"option": "A2",
	//				"text": "1"
	//			}, {
	//				"option": "B2",
	//				"text": "2"
	//			}]
	//	}
	SurveyRecords *xtype.RawMessage `protobuf:"bytes,1,opt,name=survey_records,json=surveyRecords,proto3" json:"survey_records,omitempty"`
	// schema配置
	Schema *xtype.RawMessage `protobuf:"bytes,2,opt,name=schema,proto3" json:"schema,omitempty"`
	// materials config
	MaterialsConfig *xtype.RawMessage `protobuf:"bytes,3,opt,name=materials_config,json=materialsConfig,proto3" json:"materials_config,omitempty"`
	// web_settings
	WebSettings *xtype.RawMessage `protobuf:"bytes,4,opt,name=web_settings,json=webSettings,proto3" json:"web_settings,omitempty"`
	Name        string            `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *AnswerResponse) Reset() {
	*x = AnswerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnswerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnswerResponse) ProtoMessage() {}

func (x *AnswerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnswerResponse.ProtoReflect.Descriptor instead.
func (*AnswerResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{5}
}

func (x *AnswerResponse) GetSurveyRecords() *xtype.RawMessage {
	if x != nil {
		return x.SurveyRecords
	}
	return nil
}

func (x *AnswerResponse) GetSchema() *xtype.RawMessage {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *AnswerResponse) GetMaterialsConfig() *xtype.RawMessage {
	if x != nil {
		return x.MaterialsConfig
	}
	return nil
}

func (x *AnswerResponse) GetWebSettings() *xtype.RawMessage {
	if x != nil {
		return x.WebSettings
	}
	return nil
}

func (x *AnswerResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Request message for SurveyService.Signin
type SigninRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户Token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 问卷ID
	SurveyId string `protobuf:"bytes,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	// 用户ID
	Openid string `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid"`
	// 角色ID
	RoleId string `protobuf:"bytes,4,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// 设备ID
	DeviceId string `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	// 时区
	Timezone string `protobuf:"bytes,6,opt,name=timezone,proto3" json:"timezone,omitempty"`
	// 问卷组
	Group string `protobuf:"bytes,7,opt,name=group,proto3" json:"group,omitempty"`
}

func (x *SigninRequest) Reset() {
	*x = SigninRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SigninRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SigninRequest) ProtoMessage() {}

func (x *SigninRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SigninRequest.ProtoReflect.Descriptor instead.
func (*SigninRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{6}
}

func (x *SigninRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SigninRequest) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

func (x *SigninRequest) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *SigninRequest) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *SigninRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SigninRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *SigninRequest) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

// Response message for SurveyService.Signin
type SigninResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The authority, x-Authority
	Authority string `protobuf:"bytes,1,opt,name=authority,proto3" json:"authority,omitempty"`
	// 发奖信息 错误码1004时有值
	AwardInfo *AwardInfo `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	// 签名字段 错误码1004时有值
	Sign string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`
}

func (x *SigninResponse) Reset() {
	*x = SigninResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SigninResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SigninResponse) ProtoMessage() {}

func (x *SigninResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SigninResponse.ProtoReflect.Descriptor instead.
func (*SigninResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{7}
}

func (x *SigninResponse) GetAuthority() string {
	if x != nil {
		return x.Authority
	}
	return ""
}

func (x *SigninResponse) GetAwardInfo() *AwardInfo {
	if x != nil {
		return x.AwardInfo
	}
	return nil
}

func (x *SigninResponse) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

// Request message for SurveyService.Config
type ConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷ID
	SurveyId string `protobuf:"bytes,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	Lang     string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ConfigRequest) Reset() {
	*x = ConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigRequest) ProtoMessage() {}

func (x *ConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigRequest.ProtoReflect.Descriptor instead.
func (*ConfigRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{8}
}

func (x *ConfigRequest) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

func (x *ConfigRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

// Response message for SurveyService.Config
type ConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schema配置
	Schema *xtype.RawMessage `protobuf:"bytes,1,opt,name=schema,proto3" json:"schema"`
	// materials config
	MaterialsConfig *xtype.RawMessage `protobuf:"bytes,2,opt,name=materials_config,json=materialsConfig,proto3" json:"materials_config"`
	// web_settings
	WebSettings *xtype.RawMessage `protobuf:"bytes,3,opt,name=web_settings,json=webSettings,proto3" json:"web_settings"`
	// name
	Name string            `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	Font *xtype.RawMessage `protobuf:"bytes,5,opt,name=font,proto3" json:"font"`
}

func (x *ConfigResponse) Reset() {
	*x = ConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigResponse) ProtoMessage() {}

func (x *ConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigResponse.ProtoReflect.Descriptor instead.
func (*ConfigResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{9}
}

func (x *ConfigResponse) GetSchema() *xtype.RawMessage {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *ConfigResponse) GetMaterialsConfig() *xtype.RawMessage {
	if x != nil {
		return x.MaterialsConfig
	}
	return nil
}

func (x *ConfigResponse) GetWebSettings() *xtype.RawMessage {
	if x != nil {
		return x.WebSettings
	}
	return nil
}

func (x *ConfigResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConfigResponse) GetFont() *xtype.RawMessage {
	if x != nil {
		return x.Font
	}
	return nil
}

// Request message for SurveyInternalService.ConfigReload
type ConfigReloadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷ID
	SurveyId string `protobuf:"bytes,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	// 问卷组ID
	Group string `protobuf:"bytes,2,opt,name=group,proto3" json:"group,omitempty"`
}

func (x *ConfigReloadRequest) Reset() {
	*x = ConfigReloadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigReloadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigReloadRequest) ProtoMessage() {}

func (x *ConfigReloadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigReloadRequest.ProtoReflect.Descriptor instead.
func (*ConfigReloadRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{10}
}

func (x *ConfigReloadRequest) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

func (x *ConfigReloadRequest) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

// 获取问卷组 问卷内容
type GroupConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷ID
	Group string `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	Lang  string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GroupConfigRequest) Reset() {
	*x = GroupConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupConfigRequest) ProtoMessage() {}

func (x *GroupConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupConfigRequest.ProtoReflect.Descriptor instead.
func (*GroupConfigRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{11}
}

func (x *GroupConfigRequest) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *GroupConfigRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GroupConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷组
	Group string `protobuf:"bytes,6,opt,name=group,proto3" json:"group"`
	// 问卷id
	SurveyId string `protobuf:"bytes,7,opt,name=survey_id,json=surveyId,proto3" json:"survey_id"`
}

func (x *GroupConfigResponse) Reset() {
	*x = GroupConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupConfigResponse) ProtoMessage() {}

func (x *GroupConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupConfigResponse.ProtoReflect.Descriptor instead.
func (*GroupConfigResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{12}
}

func (x *GroupConfigResponse) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *GroupConfigResponse) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

var File_proto_survey_proto protoreflect.FileDescriptor

var file_proto_survey_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76,
	0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x72, 0x61, 0x77, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x0f, 0x0a, 0x0d, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xa4, 0x01, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a,
	0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0xe2, 0x41, 0x01, 0x02, 0xba, 0x47, 0x05, 0x78, 0x0e, 0x80, 0x01, 0x0a, 0x52, 0x08,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x74, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x74, 0x69, 0x64, 0x22, 0x6e, 0x0a, 0x14,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x61, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x61,
	0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x22, 0x94, 0x01, 0x0a,
	0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xd2, 0xa7, 0x86, 0x07, 0x13, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0xd2, 0xa7, 0x86, 0x07, 0x14, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x64, 0x22, 0xe8, 0x01, 0x0a, 0x0d, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xe2, 0x41, 0x01, 0x02, 0xba, 0x47,
	0x05, 0x78, 0x0e, 0x80, 0x01, 0x0a, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64,
	0x12, 0x2e, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x16, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x12, 0x30, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x36, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86,
	0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61,
	0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xa1,
	0x02, 0x0a, 0x0e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x41, 0x0a, 0x0e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0d, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x45, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0f,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x3d, 0x0a, 0x0c, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0xa4, 0x02, 0x0a, 0x0d, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x06, 0xba, 0x47, 0x03, 0x78, 0x80, 0x08, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x29, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xe2, 0x41, 0x01, 0x02, 0xba, 0x47, 0x05, 0x78, 0x0e,
	0x80, 0x01, 0x0a, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0xba,
	0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x30, 0x0a,
	0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x36, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x19, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x86, 0x01, 0x0a, 0x0e, 0x53, 0x69,
	0x67, 0x6e, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x42, 0x0a, 0x0a, 0x61, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69,
	0x67, 0x6e, 0x22, 0x46, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x95, 0x02, 0x0a, 0x0e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a,
	0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52,
	0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x12, 0x45, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x0c, 0x77, 0x65, 0x62, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x66,
	0x6f, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x04, 0x66, 0x6f, 0x6e, 0x74, 0x3a, 0x05, 0xc8, 0xa7, 0x86,
	0x07, 0x01, 0x22, 0x48, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x6c, 0x6f,
	0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x44, 0x0a, 0x12,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61,
	0x6e, 0x67, 0x22, 0x4f, 0x0a, 0x13, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x3a, 0x05, 0xc8, 0xa7,
	0x86, 0x07, 0x01, 0x32, 0xda, 0x01, 0x0a, 0x12, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0xa4, 0x01, 0x0a, 0x0c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x2d, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x4e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x22, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x72, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0xda, 0xf6, 0xd1, 0xb6,
	0x05, 0x1f, 0x0a, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x1a, 0x1d, 0xca, 0x41, 0x1a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2d, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x32, 0x9a, 0x06, 0x0a, 0x0d, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x12, 0x2d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x04, 0x62, 0x6f, 0x64, 0x79,
	0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x12, 0x76, 0x0a, 0x06, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x12, 0x27, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x12, 0x76, 0x0a, 0x06, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13,
	0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x76, 0x0a, 0x06, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x27, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x8b, 0x01, 0x0a, 0x0b,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2c, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19,
	0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x63, 0x0a, 0x06, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x12, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x1a, 0x1d,
	0xca, 0x41, 0x1a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2d, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x42, 0x5d, 0x0a,
	0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x2e, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_proto_rawDescOnce sync.Once
	file_proto_survey_proto_rawDescData = file_proto_survey_proto_rawDesc
)

func file_proto_survey_proto_rawDescGZIP() []byte {
	file_proto_survey_proto_rawDescOnce.Do(func() {
		file_proto_survey_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_proto_rawDescData)
	})
	return file_proto_survey_proto_rawDescData
}

var file_proto_survey_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_proto_survey_proto_goTypes = []any{
	(*HealthRequest)(nil),        // 0: papegames.sparrow.survey.HealthRequest
	(*SubmitSurveyRequest)(nil),  // 1: papegames.sparrow.survey.SubmitSurveyRequest
	(*SubmitSurveyResponse)(nil), // 2: papegames.sparrow.survey.SubmitSurveyResponse
	(*AwardInfo)(nil),            // 3: papegames.sparrow.survey.AwardInfo
	(*AnswerRequest)(nil),        // 4: papegames.sparrow.survey.AnswerRequest
	(*AnswerResponse)(nil),       // 5: papegames.sparrow.survey.AnswerResponse
	(*SigninRequest)(nil),        // 6: papegames.sparrow.survey.SigninRequest
	(*SigninResponse)(nil),       // 7: papegames.sparrow.survey.SigninResponse
	(*ConfigRequest)(nil),        // 8: papegames.sparrow.survey.ConfigRequest
	(*ConfigResponse)(nil),       // 9: papegames.sparrow.survey.ConfigResponse
	(*ConfigReloadRequest)(nil),  // 10: papegames.sparrow.survey.ConfigReloadRequest
	(*GroupConfigRequest)(nil),   // 11: papegames.sparrow.survey.GroupConfigRequest
	(*GroupConfigResponse)(nil),  // 12: papegames.sparrow.survey.GroupConfigResponse
	(*xtype.RawMessage)(nil),     // 13: papegames.type.RawMessage
	(*xtype.Empty)(nil),          // 14: papegames.type.Empty
}
var file_proto_survey_proto_depIdxs = []int32{
	3,  // 0: papegames.sparrow.survey.SubmitSurveyResponse.award_info:type_name -> papegames.sparrow.survey.AwardInfo
	13, // 1: papegames.sparrow.survey.AnswerResponse.survey_records:type_name -> papegames.type.RawMessage
	13, // 2: papegames.sparrow.survey.AnswerResponse.schema:type_name -> papegames.type.RawMessage
	13, // 3: papegames.sparrow.survey.AnswerResponse.materials_config:type_name -> papegames.type.RawMessage
	13, // 4: papegames.sparrow.survey.AnswerResponse.web_settings:type_name -> papegames.type.RawMessage
	3,  // 5: papegames.sparrow.survey.SigninResponse.award_info:type_name -> papegames.sparrow.survey.AwardInfo
	13, // 6: papegames.sparrow.survey.ConfigResponse.schema:type_name -> papegames.type.RawMessage
	13, // 7: papegames.sparrow.survey.ConfigResponse.materials_config:type_name -> papegames.type.RawMessage
	13, // 8: papegames.sparrow.survey.ConfigResponse.web_settings:type_name -> papegames.type.RawMessage
	13, // 9: papegames.sparrow.survey.ConfigResponse.font:type_name -> papegames.type.RawMessage
	10, // 10: papegames.sparrow.survey.SurveyServiceAdmin.ConfigReload:input_type -> papegames.sparrow.survey.ConfigReloadRequest
	1,  // 11: papegames.sparrow.survey.SurveyService.SubmitSurvey:input_type -> papegames.sparrow.survey.SubmitSurveyRequest
	6,  // 12: papegames.sparrow.survey.SurveyService.Signin:input_type -> papegames.sparrow.survey.SigninRequest
	8,  // 13: papegames.sparrow.survey.SurveyService.Config:input_type -> papegames.sparrow.survey.ConfigRequest
	4,  // 14: papegames.sparrow.survey.SurveyService.Answer:input_type -> papegames.sparrow.survey.AnswerRequest
	11, // 15: papegames.sparrow.survey.SurveyService.GroupConfig:input_type -> papegames.sparrow.survey.GroupConfigRequest
	0,  // 16: papegames.sparrow.survey.SurveyService.Health:input_type -> papegames.sparrow.survey.HealthRequest
	14, // 17: papegames.sparrow.survey.SurveyServiceAdmin.ConfigReload:output_type -> papegames.type.Empty
	2,  // 18: papegames.sparrow.survey.SurveyService.SubmitSurvey:output_type -> papegames.sparrow.survey.SubmitSurveyResponse
	7,  // 19: papegames.sparrow.survey.SurveyService.Signin:output_type -> papegames.sparrow.survey.SigninResponse
	9,  // 20: papegames.sparrow.survey.SurveyService.Config:output_type -> papegames.sparrow.survey.ConfigResponse
	5,  // 21: papegames.sparrow.survey.SurveyService.Answer:output_type -> papegames.sparrow.survey.AnswerResponse
	12, // 22: papegames.sparrow.survey.SurveyService.GroupConfig:output_type -> papegames.sparrow.survey.GroupConfigResponse
	14, // 23: papegames.sparrow.survey.SurveyService.Health:output_type -> papegames.type.Empty
	17, // [17:24] is the sub-list for method output_type
	10, // [10:17] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proto_survey_proto_init() }
func file_proto_survey_proto_init() {
	if File_proto_survey_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*HealthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SubmitSurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SubmitSurveyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*AwardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*AnswerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*AnswerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*SigninRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*SigninResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ConfigReloadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GroupConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GroupConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_proto_survey_proto_goTypes,
		DependencyIndexes: file_proto_survey_proto_depIdxs,
		MessageInfos:      file_proto_survey_proto_msgTypes,
	}.Build()
	File_proto_survey_proto = out.File
	file_proto_survey_proto_rawDesc = nil
	file_proto_survey_proto_goTypes = nil
	file_proto_survey_proto_depIdxs = nil
}
