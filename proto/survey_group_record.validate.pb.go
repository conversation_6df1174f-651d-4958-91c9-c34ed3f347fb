// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_group_record.proto

package proto

func (x *SurveyGroupRecord) Validate() error {
	if len(x.GetOpenid()) > 128 {
		return SurveyGroupRecordValidationError{
			field:   "Openid",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.GetRoleId()) > 128 {
		return SurveyGroupRecordValidationError{
			field:   "RoleId",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.GetDeviceId()) > 200 {
		return SurveyGroupRecordValidationError{
			field:   "DeviceId",
			reason:  "max_length",
			message: "value length must be at most 200 bytes",
		}
	}
	if len(x.GetIp()) > 200 {
		return SurveyGroupRecordValidationError{
			field:   "Ip",
			reason:  "max_length",
			message: "value length must be at most 200 bytes",
		}
	}
	return nil
}

type SurveyGroupRecordValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupRecordValidationError) Field() string { return e.field }

func (e SurveyGroupRecordValidationError) Reason() string { return e.reason }

func (e SurveyGroupRecordValidationError) Message() string { return e.message }

func (e SurveyGroupRecordValidationError) Cause() error { return e.cause }

func (e SurveyGroupRecordValidationError) ErrorName() string {
	return "SurveyGroupRecordValidationError"
}

func (e SurveyGroupRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupRecord." + e.field + ": " + e.message + cause
}
