// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/pre_award.proto

package proto

import (
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PreAward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Clientid  string           `protobuf:"bytes,2,opt,name=clientid,proto3" json:"clientid,omitempty"`
	Platform  int32            `protobuf:"varint,3,opt,name=platform,proto3" json:"platform,omitempty"`
	Zoneid    int32            `protobuf:"varint,4,opt,name=zoneid,proto3" json:"zoneid,omitempty"`
	Openid    string           `protobuf:"bytes,5,opt,name=openid,proto3" json:"openid,omitempty"`
	Sender    string           `protobuf:"bytes,6,opt,name=sender,proto3" json:"sender,omitempty"`
	Title     string           `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	Body      string           `protobuf:"bytes,8,opt,name=body,proto3" json:"body,omitempty"`
	Content   string           `protobuf:"bytes,9,opt,name=content,proto3" json:"content,omitempty"`
	Stime     *xtype.Timestamp `protobuf:"bytes,10,opt,name=stime,proto3" json:"stime,omitempty"`
	Etime     *xtype.Timestamp `protobuf:"bytes,11,opt,name=etime,proto3" json:"etime,omitempty"`
	Permanent int32            `protobuf:"varint,12,opt,name=permanent,proto3" json:"permanent,omitempty"`
	Mtime     *xtype.Timestamp `protobuf:"bytes,13,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Ctime     *xtype.Timestamp `protobuf:"bytes,14,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Isgot     int32            `protobuf:"varint,15,opt,name=isgot,proto3" json:"isgot,omitempty"`
}

func (x *PreAward) Reset() {
	*x = PreAward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pre_award_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAward) ProtoMessage() {}

func (x *PreAward) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pre_award_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAward.ProtoReflect.Descriptor instead.
func (*PreAward) Descriptor() ([]byte, []int) {
	return file_proto_pre_award_proto_rawDescGZIP(), []int{0}
}

func (x *PreAward) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PreAward) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *PreAward) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *PreAward) GetZoneid() int32 {
	if x != nil {
		return x.Zoneid
	}
	return 0
}

func (x *PreAward) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *PreAward) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *PreAward) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *PreAward) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *PreAward) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *PreAward) GetStime() *xtype.Timestamp {
	if x != nil {
		return x.Stime
	}
	return nil
}

func (x *PreAward) GetEtime() *xtype.Timestamp {
	if x != nil {
		return x.Etime
	}
	return nil
}

func (x *PreAward) GetPermanent() int32 {
	if x != nil {
		return x.Permanent
	}
	return 0
}

func (x *PreAward) GetMtime() *xtype.Timestamp {
	if x != nil {
		return x.Mtime
	}
	return nil
}

func (x *PreAward) GetCtime() *xtype.Timestamp {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *PreAward) GetIsgot() int32 {
	if x != nil {
		return x.Isgot
	}
	return 0
}

var File_proto_pre_award_proto protoreflect.FileDescriptor

var file_proto_pre_award_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x77, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x1a, 0x1e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xd6, 0x03, 0x0a, 0x08, 0x50, 0x72, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x7a, 0x6f, 0x6e, 0x65, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x7a, 0x6f, 0x6e, 0x65, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x73, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x6d, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x63,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x67, 0x6f, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x73, 0x67, 0x6f, 0x74, 0x42, 0x5d, 0x0a, 0x1c, 0x63, 0x6f,
	0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_proto_pre_award_proto_rawDescOnce sync.Once
	file_proto_pre_award_proto_rawDescData = file_proto_pre_award_proto_rawDesc
)

func file_proto_pre_award_proto_rawDescGZIP() []byte {
	file_proto_pre_award_proto_rawDescOnce.Do(func() {
		file_proto_pre_award_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_pre_award_proto_rawDescData)
	})
	return file_proto_pre_award_proto_rawDescData
}

var file_proto_pre_award_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_pre_award_proto_goTypes = []any{
	(*PreAward)(nil),        // 0: papegames.sparrow.survey.PreAward
	(*xtype.Timestamp)(nil), // 1: papegames.type.Timestamp
}
var file_proto_pre_award_proto_depIdxs = []int32{
	1, // 0: papegames.sparrow.survey.PreAward.stime:type_name -> papegames.type.Timestamp
	1, // 1: papegames.sparrow.survey.PreAward.etime:type_name -> papegames.type.Timestamp
	1, // 2: papegames.sparrow.survey.PreAward.mtime:type_name -> papegames.type.Timestamp
	1, // 3: papegames.sparrow.survey.PreAward.ctime:type_name -> papegames.type.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proto_pre_award_proto_init() }
func file_proto_pre_award_proto_init() {
	if File_proto_pre_award_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_pre_award_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PreAward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_pre_award_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_pre_award_proto_goTypes,
		DependencyIndexes: file_proto_pre_award_proto_depIdxs,
		MessageInfos:      file_proto_pre_award_proto_msgTypes,
	}.Build()
	File_proto_pre_award_proto = out.File
	file_proto_pre_award_proto_rawDesc = nil
	file_proto_pre_award_proto_goTypes = nil
	file_proto_pre_award_proto_depIdxs = nil
}
