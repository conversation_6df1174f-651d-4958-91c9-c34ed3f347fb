// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_record.proto

package proto

func (x *SurveyRecord) Validate() error {
	if len(x.GetRoleId()) > 128 {
		return SurveyRecordValidationError{
			field:   "RoleId",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.Get<PERSON>penid()) > 128 {
		return SurveyRecordValidationError{
			field:   "Openid",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.GetDeviceId()) > 200 {
		return SurveyRecordValidationError{
			field:   "DeviceId",
			reason:  "max_length",
			message: "value length must be at most 200 bytes",
		}
	}
	if len(x.GetIp()) > 200 {
		return SurveyRecordValidationError{
			field:   "Ip",
			reason:  "max_length",
			message: "value length must be at most 200 bytes",
		}
	}
	return nil
}

func (x *SurveyRecordDetail) Validate() error {
	if len(x.GetQuestion()) < 6 {
		return SurveyRecordDetailValidationError{
			field:   "Question",
			reason:  "min_length",
			message: "value must be at least 6 bytes",
		}
	}
	if len(x.GetQuestion()) > 64 {
		return SurveyRecordDetailValidationError{
			field:   "Question",
			reason:  "max_length",
			message: "value length must be at most 64 bytes",
		}
	}
	if len(x.GetOption()) < 6 {
		return SurveyRecordDetailValidationError{
			field:   "Option",
			reason:  "min_length",
			message: "value must be at least 6 bytes",
		}
	}
	if len(x.GetOption()) > 64 {
		return SurveyRecordDetailValidationError{
			field:   "Option",
			reason:  "max_length",
			message: "value length must be at most 64 bytes",
		}
	}
	if len(x.GetText()) > 256 {
		return SurveyRecordDetailValidationError{
			field:   "Text",
			reason:  "max_length",
			message: "value length must be at most 256 bytes",
		}
	}
	return nil
}

type SurveyRecordValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordValidationError) Field() string { return e.field }

func (e SurveyRecordValidationError) Reason() string { return e.reason }

func (e SurveyRecordValidationError) Message() string { return e.message }

func (e SurveyRecordValidationError) Cause() error { return e.cause }

func (e SurveyRecordValidationError) ErrorName() string { return "SurveyRecordValidationError" }

func (e SurveyRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecord." + e.field + ": " + e.message + cause
}

type SurveyRecordDetailValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordDetailValidationError) Field() string { return e.field }

func (e SurveyRecordDetailValidationError) Reason() string { return e.reason }

func (e SurveyRecordDetailValidationError) Message() string { return e.message }

func (e SurveyRecordDetailValidationError) Cause() error { return e.cause }

func (e SurveyRecordDetailValidationError) ErrorName() string {
	return "SurveyRecordDetailValidationError"
}

func (e SurveyRecordDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordDetail." + e.field + ": " + e.message + cause
}
