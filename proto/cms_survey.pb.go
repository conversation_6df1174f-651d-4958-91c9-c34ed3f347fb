// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/cms_survey.proto

package proto

import (
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// unit
type CmsSurveyVersion_PeriodicControl_Unit int32

const (
	CmsSurveyVersion_PeriodicControl_Unit_Unknown CmsSurveyVersion_PeriodicControl_Unit = 0
	CmsSurveyVersion_PeriodicControl_Unit_Minute  CmsSurveyVersion_PeriodicControl_Unit = 1
	CmsSurveyVersion_PeriodicControl_Unit_Hour    CmsSurveyVersion_PeriodicControl_Unit = 2
	CmsSurveyVersion_PeriodicControl_Unit_Day     CmsSurveyVersion_PeriodicControl_Unit = 3
)

// Enum value maps for CmsSurveyVersion_PeriodicControl_Unit.
var (
	CmsSurveyVersion_PeriodicControl_Unit_name = map[int32]string{
		0: "Unit_Unknown",
		1: "Unit_Minute",
		2: "Unit_Hour",
		3: "Unit_Day",
	}
	CmsSurveyVersion_PeriodicControl_Unit_value = map[string]int32{
		"Unit_Unknown": 0,
		"Unit_Minute":  1,
		"Unit_Hour":    2,
		"Unit_Day":     3,
	}
)

func (x CmsSurveyVersion_PeriodicControl_Unit) Enum() *CmsSurveyVersion_PeriodicControl_Unit {
	p := new(CmsSurveyVersion_PeriodicControl_Unit)
	*p = x
	return p
}

func (x CmsSurveyVersion_PeriodicControl_Unit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CmsSurveyVersion_PeriodicControl_Unit) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_cms_survey_proto_enumTypes[0].Descriptor()
}

func (CmsSurveyVersion_PeriodicControl_Unit) Type() protoreflect.EnumType {
	return &file_proto_cms_survey_proto_enumTypes[0]
}

func (x CmsSurveyVersion_PeriodicControl_Unit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CmsSurveyVersion_PeriodicControl_Unit.Descriptor instead.
func (CmsSurveyVersion_PeriodicControl_Unit) EnumDescriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 5, 0}
}

type CmsSurveyVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                       int64                     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" gorm:"primaryKey"`
	Clientid                 string                    `protobuf:"bytes,2,opt,name=clientid,proto3" json:"clientid,omitempty"`
	Name                     string                    `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	IsClosed                 int32                     `protobuf:"varint,4,opt,name=is_closed,json=isClosed,proto3" json:"is_closed,omitempty"`
	IsPause                  int32                     `protobuf:"varint,5,opt,name=is_pause,json=isPause,proto3" json:"is_pause,omitempty"`
	Stime                    *xtype.Timestamp          `protobuf:"bytes,6,opt,name=stime,proto3" json:"stime,omitempty"`
	Etime                    *xtype.Timestamp          `protobuf:"bytes,7,opt,name=etime,proto3" json:"etime,omitempty"`
	Settings                 string                    `protobuf:"bytes,8,opt,name=settings,proto3" json:"settings,omitempty"`
	SettingStruct            *CmsSurveyVersion_Setting `protobuf:"bytes,9,opt,name=settingStruct,proto3" json:"settingStruct,omitempty" gorm:"-"`
	HashCode                 string                    `protobuf:"bytes,10,opt,name=hash_code,json=hashCode,proto3" json:"hash_code,omitempty"`
	IsDelete                 int32                     `protobuf:"varint,11,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	WebSettings              string                    `protobuf:"bytes,13,opt,name=web_settings,json=webSettings,proto3" json:"web_settings,omitempty"`
	DefaultLanguage          string                    `protobuf:"bytes,14,opt,name=default_language,json=defaultLanguage,proto3" json:"default_language,omitempty" gorm:"-"`
	Schema                   []byte                    `protobuf:"bytes,15,opt,name=schema,proto3" json:"schema,omitempty"`
	SchemaDec                []byte                    `protobuf:"bytes,16,opt,name=schema_dec,json=schemaDec,proto3" json:"schema_dec,omitempty" gorm:"-"`
	MultilingualSchema       []byte                    `protobuf:"bytes,17,opt,name=multilingual_schema,json=multilingualSchema,proto3" json:"multilingual_schema,omitempty"`
	MultilingualSchemaDecMap map[string][]byte         `protobuf:"bytes,18,rep,name=multilingual_schema_dec_map,json=multilingualSchemaDecMap,proto3" json:"multilingual_schema_dec_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" gorm:"-"`
	SurveyId                 int32                     `protobuf:"varint,19,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	Font                     []byte                    `protobuf:"bytes,20,opt,name=font,proto3" json:"font,omitempty"`
	KeyValue                 string                    `protobuf:"bytes,21,opt,name=key_value,json=keyValue,proto3" json:"key_value" gorm:"type:text;comment:中文/英文文案"`
	KeyValueMap              map[string]string         `protobuf:"bytes,22,rep,name=key_value_map,json=keyValueMap,proto3" json:"key_value_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" gorm:"-"`
}

func (x *CmsSurveyVersion) Reset() {
	*x = CmsSurveyVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion) ProtoMessage() {}

func (x *CmsSurveyVersion) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0}
}

func (x *CmsSurveyVersion) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CmsSurveyVersion) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *CmsSurveyVersion) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CmsSurveyVersion) GetIsClosed() int32 {
	if x != nil {
		return x.IsClosed
	}
	return 0
}

func (x *CmsSurveyVersion) GetIsPause() int32 {
	if x != nil {
		return x.IsPause
	}
	return 0
}

func (x *CmsSurveyVersion) GetStime() *xtype.Timestamp {
	if x != nil {
		return x.Stime
	}
	return nil
}

func (x *CmsSurveyVersion) GetEtime() *xtype.Timestamp {
	if x != nil {
		return x.Etime
	}
	return nil
}

func (x *CmsSurveyVersion) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

func (x *CmsSurveyVersion) GetSettingStruct() *CmsSurveyVersion_Setting {
	if x != nil {
		return x.SettingStruct
	}
	return nil
}

func (x *CmsSurveyVersion) GetHashCode() string {
	if x != nil {
		return x.HashCode
	}
	return ""
}

func (x *CmsSurveyVersion) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *CmsSurveyVersion) GetWebSettings() string {
	if x != nil {
		return x.WebSettings
	}
	return ""
}

func (x *CmsSurveyVersion) GetDefaultLanguage() string {
	if x != nil {
		return x.DefaultLanguage
	}
	return ""
}

func (x *CmsSurveyVersion) GetSchema() []byte {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *CmsSurveyVersion) GetSchemaDec() []byte {
	if x != nil {
		return x.SchemaDec
	}
	return nil
}

func (x *CmsSurveyVersion) GetMultilingualSchema() []byte {
	if x != nil {
		return x.MultilingualSchema
	}
	return nil
}

func (x *CmsSurveyVersion) GetMultilingualSchemaDecMap() map[string][]byte {
	if x != nil {
		return x.MultilingualSchemaDecMap
	}
	return nil
}

func (x *CmsSurveyVersion) GetSurveyId() int32 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *CmsSurveyVersion) GetFont() []byte {
	if x != nil {
		return x.Font
	}
	return nil
}

func (x *CmsSurveyVersion) GetKeyValue() string {
	if x != nil {
		return x.KeyValue
	}
	return ""
}

func (x *CmsSurveyVersion) GetKeyValueMap() map[string]string {
	if x != nil {
		return x.KeyValueMap
	}
	return nil
}

type CmsSurveyVersion_Setting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRuleConfig    *CmsSurveyVersion_BaseRuleConfig    `protobuf:"bytes,1,opt,name=baseRuleConfig,proto3" json:"baseRuleConfig,omitempty"`
	AnswerLimitConfig *CmsSurveyVersion_AnswerLimitConfig `protobuf:"bytes,2,opt,name=answerLimitConfig,proto3" json:"answerLimitConfig,omitempty"`
	GiftConfig        *CmsSurveyVersion_GiftConfig        `protobuf:"bytes,3,opt,name=giftConfig,proto3" json:"giftConfig,omitempty"`
	MaterialsConfig   *xtype.RawMessage                   `protobuf:"bytes,4,opt,name=materialsConfig,proto3" json:"materialsConfig,omitempty"`
	// 区服校验
	ZoneIds []int32 `protobuf:"varint,14,rep,packed,name=zoneIds,proto3" json:"zoneIds,omitempty"`
}

func (x *CmsSurveyVersion_Setting) Reset() {
	*x = CmsSurveyVersion_Setting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_Setting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_Setting) ProtoMessage() {}

func (x *CmsSurveyVersion_Setting) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_Setting.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_Setting) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CmsSurveyVersion_Setting) GetBaseRuleConfig() *CmsSurveyVersion_BaseRuleConfig {
	if x != nil {
		return x.BaseRuleConfig
	}
	return nil
}

func (x *CmsSurveyVersion_Setting) GetAnswerLimitConfig() *CmsSurveyVersion_AnswerLimitConfig {
	if x != nil {
		return x.AnswerLimitConfig
	}
	return nil
}

func (x *CmsSurveyVersion_Setting) GetGiftConfig() *CmsSurveyVersion_GiftConfig {
	if x != nil {
		return x.GiftConfig
	}
	return nil
}

func (x *CmsSurveyVersion_Setting) GetMaterialsConfig() *xtype.RawMessage {
	if x != nil {
		return x.MaterialsConfig
	}
	return nil
}

func (x *CmsSurveyVersion_Setting) GetZoneIds() []int32 {
	if x != nil {
		return x.ZoneIds
	}
	return nil
}

type CmsSurveyVersion_WebSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageList []string `protobuf:"bytes,1,rep,name=languageList,proto3" json:"languageList,omitempty"`
}

func (x *CmsSurveyVersion_WebSettings) Reset() {
	*x = CmsSurveyVersion_WebSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_WebSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_WebSettings) ProtoMessage() {}

func (x *CmsSurveyVersion_WebSettings) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_WebSettings.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_WebSettings) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 1}
}

func (x *CmsSurveyVersion_WebSettings) GetLanguageList() []string {
	if x != nil {
		return x.LanguageList
	}
	return nil
}

type CmsSurveyVersion_AnswerLimitConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限制方式 0 设备限制  1 IP限制
	LimitType string `protobuf:"bytes,1,opt,name=limitType,proto3" json:"limitType,omitempty"`
}

func (x *CmsSurveyVersion_AnswerLimitConfig) Reset() {
	*x = CmsSurveyVersion_AnswerLimitConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_AnswerLimitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_AnswerLimitConfig) ProtoMessage() {}

func (x *CmsSurveyVersion_AnswerLimitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_AnswerLimitConfig.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_AnswerLimitConfig) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 2}
}

func (x *CmsSurveyVersion_AnswerLimitConfig) GetLimitType() string {
	if x != nil {
		return x.LimitType
	}
	return ""
}

type CmsSurveyVersion_BaseRuleConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 登陆方式  0 : 角色登陆 1: 账号登陆 2: 不登录
	LoginType         string                              `protobuf:"bytes,1,opt,name=loginType,proto3" json:"loginType,omitempty"`
	AnswerTimesConfig *CmsSurveyVersion_AnswerTimesConfig `protobuf:"bytes,2,opt,name=answerTimesConfig,proto3" json:"answerTimesConfig,omitempty"`
	// 频率控制
	PeriodicControl *CmsSurveyVersion_PeriodicControl `protobuf:"bytes,3,opt,name=periodicControl,proto3" json:"periodicControl,omitempty"`
}

func (x *CmsSurveyVersion_BaseRuleConfig) Reset() {
	*x = CmsSurveyVersion_BaseRuleConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_BaseRuleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_BaseRuleConfig) ProtoMessage() {}

func (x *CmsSurveyVersion_BaseRuleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_BaseRuleConfig.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_BaseRuleConfig) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 3}
}

func (x *CmsSurveyVersion_BaseRuleConfig) GetLoginType() string {
	if x != nil {
		return x.LoginType
	}
	return ""
}

func (x *CmsSurveyVersion_BaseRuleConfig) GetAnswerTimesConfig() *CmsSurveyVersion_AnswerTimesConfig {
	if x != nil {
		return x.AnswerTimesConfig
	}
	return nil
}

func (x *CmsSurveyVersion_BaseRuleConfig) GetPeriodicControl() *CmsSurveyVersion_PeriodicControl {
	if x != nil {
		return x.PeriodicControl
	}
	return nil
}

type CmsSurveyVersion_AnswerTimesConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1: 仅一次 2: 限定次数 3: 无限制
	LimitType int32 `protobuf:"varint,1,opt,name=limitType,proto3" json:"limitType,omitempty"`
	// 次数
	Times int32 `protobuf:"varint,2,opt,name=times,proto3" json:"times,omitempty"`
}

func (x *CmsSurveyVersion_AnswerTimesConfig) Reset() {
	*x = CmsSurveyVersion_AnswerTimesConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_AnswerTimesConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_AnswerTimesConfig) ProtoMessage() {}

func (x *CmsSurveyVersion_AnswerTimesConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_AnswerTimesConfig.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_AnswerTimesConfig) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 4}
}

func (x *CmsSurveyVersion_AnswerTimesConfig) GetLimitType() int32 {
	if x != nil {
		return x.LimitType
	}
	return 0
}

func (x *CmsSurveyVersion_AnswerTimesConfig) GetTimes() int32 {
	if x != nil {
		return x.Times
	}
	return 0
}

// 频率空配置
type CmsSurveyVersion_PeriodicControl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 开关，默认关闭
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// 循环值
	Interval int32 `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`
	// 时间单位
	Unit CmsSurveyVersion_PeriodicControl_Unit `protobuf:"varint,3,opt,name=unit,proto3,enum=papegames.sparrow.survey.CmsSurveyVersion_PeriodicControl_Unit" json:"unit,omitempty"`
}

func (x *CmsSurveyVersion_PeriodicControl) Reset() {
	*x = CmsSurveyVersion_PeriodicControl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_PeriodicControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_PeriodicControl) ProtoMessage() {}

func (x *CmsSurveyVersion_PeriodicControl) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_PeriodicControl.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_PeriodicControl) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 5}
}

func (x *CmsSurveyVersion_PeriodicControl) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *CmsSurveyVersion_PeriodicControl) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *CmsSurveyVersion_PeriodicControl) GetUnit() CmsSurveyVersion_PeriodicControl_Unit {
	if x != nil {
		return x.Unit
	}
	return CmsSurveyVersion_PeriodicControl_Unit_Unknown
}

type CmsSurveyVersion_GiftConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否由CMS发奖
	IsGiveOutByCms bool `protobuf:"varint,1,opt,name=isGiveOutByCms,proto3" json:"isGiveOutByCms,omitempty"`
	// 0: 邮件 1:兑换码 2:推送平台
	GiveOutType    string                           `protobuf:"bytes,2,opt,name=giveOutType,proto3" json:"giveOutType,omitempty"`
	PreAwardConfig *CmsSurveyVersion_PreAwardConfig `protobuf:"bytes,3,opt,name=preAwardConfig,proto3" json:"preAwardConfig,omitempty"`
	RedeemConfig   *CmsSurveyVersion_RedeemConfig   `protobuf:"bytes,4,opt,name=redeemConfig,proto3" json:"redeemConfig,omitempty"`
	// 0: 账号维度 1:角色维度
	GiveOutMethod string `protobuf:"bytes,5,opt,name=giveOutMethod,proto3" json:"giveOutMethod,omitempty"`
	// 推送平台邮
	PushAwardConfig *CmsSurveyVersion_PushAwardConfig `protobuf:"bytes,6,opt,name=pushAwardConfig,proto3" json:"pushAwardConfig,omitempty"`
}

func (x *CmsSurveyVersion_GiftConfig) Reset() {
	*x = CmsSurveyVersion_GiftConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_GiftConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_GiftConfig) ProtoMessage() {}

func (x *CmsSurveyVersion_GiftConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_GiftConfig.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_GiftConfig) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 6}
}

func (x *CmsSurveyVersion_GiftConfig) GetIsGiveOutByCms() bool {
	if x != nil {
		return x.IsGiveOutByCms
	}
	return false
}

func (x *CmsSurveyVersion_GiftConfig) GetGiveOutType() string {
	if x != nil {
		return x.GiveOutType
	}
	return ""
}

func (x *CmsSurveyVersion_GiftConfig) GetPreAwardConfig() *CmsSurveyVersion_PreAwardConfig {
	if x != nil {
		return x.PreAwardConfig
	}
	return nil
}

func (x *CmsSurveyVersion_GiftConfig) GetRedeemConfig() *CmsSurveyVersion_RedeemConfig {
	if x != nil {
		return x.RedeemConfig
	}
	return nil
}

func (x *CmsSurveyVersion_GiftConfig) GetGiveOutMethod() string {
	if x != nil {
		return x.GiveOutMethod
	}
	return ""
}

func (x *CmsSurveyVersion_GiftConfig) GetPushAwardConfig() *CmsSurveyVersion_PushAwardConfig {
	if x != nil {
		return x.PushAwardConfig
	}
	return nil
}

type CmsSurveyVersion_PreAwardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CmsSurveyVersion_PreAwardConfig) Reset() {
	*x = CmsSurveyVersion_PreAwardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_PreAwardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_PreAwardConfig) ProtoMessage() {}

func (x *CmsSurveyVersion_PreAwardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_PreAwardConfig.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_PreAwardConfig) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 7}
}

func (x *CmsSurveyVersion_PreAwardConfig) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CmsSurveyVersion_RedeemConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 兑换码头
	RedeemHead string `protobuf:"bytes,1,opt,name=redeemHead,proto3" json:"redeemHead,omitempty"`
}

func (x *CmsSurveyVersion_RedeemConfig) Reset() {
	*x = CmsSurveyVersion_RedeemConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_RedeemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_RedeemConfig) ProtoMessage() {}

func (x *CmsSurveyVersion_RedeemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_RedeemConfig.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_RedeemConfig) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 8}
}

func (x *CmsSurveyVersion_RedeemConfig) GetRedeemHead() string {
	if x != nil {
		return x.RedeemHead
	}
	return ""
}

type CmsSurveyVersion_PushAwardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 场景code
	SceneCode string `protobuf:"bytes,1,opt,name=sceneCode,proto3" json:"sceneCode,omitempty"`
	// 渠道code
	ChannelCode string `protobuf:"bytes,2,opt,name=channelCode,proto3" json:"channelCode,omitempty"`
	// 策略id
	StrategyId int64 `protobuf:"varint,3,opt,name=strategyId,proto3" json:"strategyId,omitempty"`
}

func (x *CmsSurveyVersion_PushAwardConfig) Reset() {
	*x = CmsSurveyVersion_PushAwardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyVersion_PushAwardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyVersion_PushAwardConfig) ProtoMessage() {}

func (x *CmsSurveyVersion_PushAwardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyVersion_PushAwardConfig.ProtoReflect.Descriptor instead.
func (*CmsSurveyVersion_PushAwardConfig) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_proto_rawDescGZIP(), []int{0, 9}
}

func (x *CmsSurveyVersion_PushAwardConfig) GetSceneCode() string {
	if x != nil {
		return x.SceneCode
	}
	return ""
}

func (x *CmsSurveyVersion_PushAwardConfig) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *CmsSurveyVersion_PushAwardConfig) GetStrategyId() int64 {
	if x != nil {
		return x.StrategyId
	}
	return 0
}

var File_proto_cms_survey_proto protoreflect.FileDescriptor

var file_proto_cms_survey_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6d, 0x73, 0x5f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x1a, 0x1e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x72, 0x61, 0x77, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67,
	0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc6, 0x16, 0x0a, 0x10, 0x43, 0x6d,
	0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0xd2, 0xa7, 0x86, 0x07,
	0x0f, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x73, 0x50, 0x61, 0x75, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x05,
	0x73, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a,
	0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x65, 0x0a, 0x0d, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x0b, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d,
	0x3a, 0x2d, 0x52, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x77,
	0x65, 0x62, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x36,
	0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x67,
	0x6f, 0x72, 0x6d, 0x3a, 0x2d, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x2a,
	0x0a, 0x0a, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f, 0x64, 0x65, 0x63, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0c, 0x42, 0x0b, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x2d, 0x52,
	0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x44, 0x65, 0x63, 0x12, 0x2f, 0x0a, 0x13, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x12, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69,
	0x6e, 0x67, 0x75, 0x61, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x94, 0x01, 0x0a, 0x1b,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x5f, 0x64, 0x65, 0x63, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x12, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x48, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x44, 0x65, 0x63, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0b, 0xd2, 0xa7, 0x86,
	0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x2d, 0x52, 0x18, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c,
	0x69, 0x6e, 0x67, 0x75, 0x61, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x44, 0x65, 0x63, 0x4d,
	0x61, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x66, 0x6f, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x66,
	0x6f, 0x6e, 0x74, 0x12, 0x5f, 0x0a, 0x09, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x42, 0x42, 0xd2, 0xa7, 0x86, 0x07, 0x2a, 0x67, 0x6f, 0x72,
	0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x74, 0x65, 0x78, 0x74, 0x3b, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x3a, 0xe4, 0xb8, 0xad, 0xe6, 0x96, 0x87, 0x2f, 0xe8, 0x8b, 0xb1, 0xe6, 0x96,
	0x87, 0xe6, 0x96, 0x87, 0xe6, 0xa1, 0x88, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x6c, 0x0a, 0x0d, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0b, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x67,
	0x6f, 0x72, 0x6d, 0x3a, 0x2d, 0x52, 0x0b, 0x6b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d,
	0x61, 0x70, 0x1a, 0x8f, 0x03, 0x0a, 0x07, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x61,
	0x0a, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x6a, 0x0a, 0x11, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x55, 0x0a,
	0x0a, 0x67, 0x69, 0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x69,
	0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x44, 0x0a, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52,
	0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x7a, 0x6f,
	0x6e, 0x65, 0x49, 0x64, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x7a, 0x6f, 0x6e,
	0x65, 0x49, 0x64, 0x73, 0x1a, 0x31, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x31, 0x0a, 0x11, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x80, 0x02, 0x0a, 0x0e, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a,
	0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6a, 0x0a, 0x11, 0x61,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x64, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x1a, 0x47, 0x0a,
	0x11, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x1a, 0xe2, 0x01, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x53,
	0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69,
	0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x04, 0x75,
	0x6e, 0x69, 0x74, 0x22, 0x46, 0x0a, 0x04, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x10, 0x0a, 0x0c, 0x55,
	0x6e, 0x69, 0x74, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a,
	0x0b, 0x55, 0x6e, 0x69, 0x74, 0x5f, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x10, 0x01, 0x12, 0x0d,
	0x0a, 0x09, 0x55, 0x6e, 0x69, 0x74, 0x5f, 0x48, 0x6f, 0x75, 0x72, 0x10, 0x02, 0x12, 0x0c, 0x0a,
	0x08, 0x55, 0x6e, 0x69, 0x74, 0x5f, 0x44, 0x61, 0x79, 0x10, 0x03, 0x1a, 0xa2, 0x03, 0x0a, 0x0a,
	0x47, 0x69, 0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x73,
	0x47, 0x69, 0x76, 0x65, 0x4f, 0x75, 0x74, 0x42, 0x79, 0x43, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x47, 0x69, 0x76, 0x65, 0x4f, 0x75, 0x74, 0x42, 0x79, 0x43,
	0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x69, 0x76, 0x65, 0x4f, 0x75, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69, 0x76, 0x65, 0x4f, 0x75, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x61, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x65, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5b, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x67, 0x69, 0x76, 0x65, 0x4f, 0x75, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x69, 0x76,
	0x65, 0x4f, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x64, 0x0a, 0x0f, 0x70, 0x75,
	0x73, 0x68, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43,
	0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0f, 0x70, 0x75, 0x73, 0x68, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x1a, 0x20, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x1a, 0x2e, 0x0a, 0x0c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x48, 0x65,
	0x61, 0x64, 0x1a, 0x71, 0x0a, 0x0f, 0x50, 0x75, 0x73, 0x68, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x49, 0x64, 0x1a, 0x4b, 0x0a, 0x1d, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69,
	0x6e, 0x67, 0x75, 0x61, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x44, 0x65, 0x63, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x42, 0x5d, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_cms_survey_proto_rawDescOnce sync.Once
	file_proto_cms_survey_proto_rawDescData = file_proto_cms_survey_proto_rawDesc
)

func file_proto_cms_survey_proto_rawDescGZIP() []byte {
	file_proto_cms_survey_proto_rawDescOnce.Do(func() {
		file_proto_cms_survey_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_cms_survey_proto_rawDescData)
	})
	return file_proto_cms_survey_proto_rawDescData
}

var file_proto_cms_survey_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_cms_survey_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_proto_cms_survey_proto_goTypes = []any{
	(CmsSurveyVersion_PeriodicControl_Unit)(0), // 0: papegames.sparrow.survey.CmsSurveyVersion.PeriodicControl.Unit
	(*CmsSurveyVersion)(nil),                   // 1: papegames.sparrow.survey.CmsSurveyVersion
	(*CmsSurveyVersion_Setting)(nil),           // 2: papegames.sparrow.survey.CmsSurveyVersion.Setting
	(*CmsSurveyVersion_WebSettings)(nil),       // 3: papegames.sparrow.survey.CmsSurveyVersion.WebSettings
	(*CmsSurveyVersion_AnswerLimitConfig)(nil), // 4: papegames.sparrow.survey.CmsSurveyVersion.AnswerLimitConfig
	(*CmsSurveyVersion_BaseRuleConfig)(nil),    // 5: papegames.sparrow.survey.CmsSurveyVersion.BaseRuleConfig
	(*CmsSurveyVersion_AnswerTimesConfig)(nil), // 6: papegames.sparrow.survey.CmsSurveyVersion.AnswerTimesConfig
	(*CmsSurveyVersion_PeriodicControl)(nil),   // 7: papegames.sparrow.survey.CmsSurveyVersion.PeriodicControl
	(*CmsSurveyVersion_GiftConfig)(nil),        // 8: papegames.sparrow.survey.CmsSurveyVersion.GiftConfig
	(*CmsSurveyVersion_PreAwardConfig)(nil),    // 9: papegames.sparrow.survey.CmsSurveyVersion.PreAwardConfig
	(*CmsSurveyVersion_RedeemConfig)(nil),      // 10: papegames.sparrow.survey.CmsSurveyVersion.RedeemConfig
	(*CmsSurveyVersion_PushAwardConfig)(nil),   // 11: papegames.sparrow.survey.CmsSurveyVersion.PushAwardConfig
	nil,                                        // 12: papegames.sparrow.survey.CmsSurveyVersion.MultilingualSchemaDecMapEntry
	nil,                                        // 13: papegames.sparrow.survey.CmsSurveyVersion.KeyValueMapEntry
	(*xtype.Timestamp)(nil),                    // 14: papegames.type.Timestamp
	(*xtype.RawMessage)(nil),                   // 15: papegames.type.RawMessage
}
var file_proto_cms_survey_proto_depIdxs = []int32{
	14, // 0: papegames.sparrow.survey.CmsSurveyVersion.stime:type_name -> papegames.type.Timestamp
	14, // 1: papegames.sparrow.survey.CmsSurveyVersion.etime:type_name -> papegames.type.Timestamp
	2,  // 2: papegames.sparrow.survey.CmsSurveyVersion.settingStruct:type_name -> papegames.sparrow.survey.CmsSurveyVersion.Setting
	12, // 3: papegames.sparrow.survey.CmsSurveyVersion.multilingual_schema_dec_map:type_name -> papegames.sparrow.survey.CmsSurveyVersion.MultilingualSchemaDecMapEntry
	13, // 4: papegames.sparrow.survey.CmsSurveyVersion.key_value_map:type_name -> papegames.sparrow.survey.CmsSurveyVersion.KeyValueMapEntry
	5,  // 5: papegames.sparrow.survey.CmsSurveyVersion.Setting.baseRuleConfig:type_name -> papegames.sparrow.survey.CmsSurveyVersion.BaseRuleConfig
	4,  // 6: papegames.sparrow.survey.CmsSurveyVersion.Setting.answerLimitConfig:type_name -> papegames.sparrow.survey.CmsSurveyVersion.AnswerLimitConfig
	8,  // 7: papegames.sparrow.survey.CmsSurveyVersion.Setting.giftConfig:type_name -> papegames.sparrow.survey.CmsSurveyVersion.GiftConfig
	15, // 8: papegames.sparrow.survey.CmsSurveyVersion.Setting.materialsConfig:type_name -> papegames.type.RawMessage
	6,  // 9: papegames.sparrow.survey.CmsSurveyVersion.BaseRuleConfig.answerTimesConfig:type_name -> papegames.sparrow.survey.CmsSurveyVersion.AnswerTimesConfig
	7,  // 10: papegames.sparrow.survey.CmsSurveyVersion.BaseRuleConfig.periodicControl:type_name -> papegames.sparrow.survey.CmsSurveyVersion.PeriodicControl
	0,  // 11: papegames.sparrow.survey.CmsSurveyVersion.PeriodicControl.unit:type_name -> papegames.sparrow.survey.CmsSurveyVersion.PeriodicControl.Unit
	9,  // 12: papegames.sparrow.survey.CmsSurveyVersion.GiftConfig.preAwardConfig:type_name -> papegames.sparrow.survey.CmsSurveyVersion.PreAwardConfig
	10, // 13: papegames.sparrow.survey.CmsSurveyVersion.GiftConfig.redeemConfig:type_name -> papegames.sparrow.survey.CmsSurveyVersion.RedeemConfig
	11, // 14: papegames.sparrow.survey.CmsSurveyVersion.GiftConfig.pushAwardConfig:type_name -> papegames.sparrow.survey.CmsSurveyVersion.PushAwardConfig
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_proto_cms_survey_proto_init() }
func file_proto_cms_survey_proto_init() {
	if File_proto_cms_survey_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_cms_survey_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_Setting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_WebSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_AnswerLimitConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_BaseRuleConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_AnswerTimesConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_PeriodicControl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_GiftConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_PreAwardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_RedeemConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyVersion_PushAwardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_cms_survey_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_cms_survey_proto_goTypes,
		DependencyIndexes: file_proto_cms_survey_proto_depIdxs,
		EnumInfos:         file_proto_cms_survey_proto_enumTypes,
		MessageInfos:      file_proto_cms_survey_proto_msgTypes,
	}.Build()
	File_proto_cms_survey_proto = out.File
	file_proto_cms_survey_proto_rawDesc = nil
	file_proto_cms_survey_proto_goTypes = nil
	file_proto_cms_survey_proto_depIdxs = nil
}
