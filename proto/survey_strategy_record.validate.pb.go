// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_strategy_record.proto

package proto

func (x *SurveyStrategyRecord) Validate() error {
	if len(x.GetOpenid()) > 64 {
		return SurveyStrategyRecordValidationError{
			field:   "Openid",
			reason:  "max_length",
			message: "value length must be at most 64 bytes",
		}
	}
	if len(x.GetRoleId()) > 64 {
		return SurveyStrategyRecordValidationError{
			field:   "RoleId",
			reason:  "max_length",
			message: "value length must be at most 64 bytes",
		}
	}
	if len(x.GetSurveyId()) > 64 {
		return SurveyStrategyRecordValidationError{
			field:   "SurveyId",
			reason:  "max_length",
			message: "value length must be at most 64 bytes",
		}
	}
	return nil
}

type SurveyStrategyRecordValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyStrategyRecordValidationError) Field() string { return e.field }

func (e SurveyStrategyRecordValidationError) Reason() string { return e.reason }

func (e SurveyStrategyRecordValidationError) Message() string { return e.message }

func (e SurveyStrategyRecordValidationError) Cause() error { return e.cause }

func (e SurveyStrategyRecordValidationError) ErrorName() string {
	return "SurveyStrategyRecordValidationError"
}

func (e SurveyStrategyRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyStrategyRecord." + e.field + ": " + e.message + cause
}
