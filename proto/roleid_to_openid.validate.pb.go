// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/roleid_to_openid.proto

package proto

func (x *RoleidToOpenid) Validate() error {
	return nil
}

type RoleidToOpenidValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RoleidToOpenidValidationError) Field() string { return e.field }

func (e RoleidToOpenidValidationError) Reason() string { return e.reason }

func (e RoleidToOpenidValidationError) Message() string { return e.message }

func (e RoleidToOpenidValidationError) Cause() error { return e.cause }

func (e RoleidToOpenidValidationError) ErrorName() string { return "RoleidToOpenidValidationError" }

func (e RoleidToOpenidValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid RoleidToOpenid." + e.field + ": " + e.message + cause
}
