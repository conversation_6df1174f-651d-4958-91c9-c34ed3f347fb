// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/cms_survey_group.proto

package proto

import (
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CmsSurveyGroupVersion_LimitType int32

const (
	CmsSurveyGroupVersion_LimitType_Unspecified CmsSurveyGroupVersion_LimitType = 0
	CmsSurveyGroupVersion_OpenIdOnce            CmsSurveyGroupVersion_LimitType = 1
	CmsSurveyGroupVersion_RoleIdOnce            CmsSurveyGroupVersion_LimitType = 2
	CmsSurveyGroupVersion_Unlimited             CmsSurveyGroupVersion_LimitType = 3
)

// Enum value maps for CmsSurveyGroupVersion_LimitType.
var (
	CmsSurveyGroupVersion_LimitType_name = map[int32]string{
		0: "LimitType_Unspecified",
		1: "OpenIdOnce",
		2: "RoleIdOnce",
		3: "Unlimited",
	}
	CmsSurveyGroupVersion_LimitType_value = map[string]int32{
		"LimitType_Unspecified": 0,
		"OpenIdOnce":            1,
		"RoleIdOnce":            2,
		"Unlimited":             3,
	}
)

func (x CmsSurveyGroupVersion_LimitType) Enum() *CmsSurveyGroupVersion_LimitType {
	p := new(CmsSurveyGroupVersion_LimitType)
	*p = x
	return p
}

func (x CmsSurveyGroupVersion_LimitType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CmsSurveyGroupVersion_LimitType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_cms_survey_group_proto_enumTypes[0].Descriptor()
}

func (CmsSurveyGroupVersion_LimitType) Type() protoreflect.EnumType {
	return &file_proto_cms_survey_group_proto_enumTypes[0]
}

func (x CmsSurveyGroupVersion_LimitType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CmsSurveyGroupVersion_LimitType.Descriptor instead.
func (CmsSurveyGroupVersion_LimitType) EnumDescriptor() ([]byte, []int) {
	return file_proto_cms_survey_group_proto_rawDescGZIP(), []int{2, 0}
}

type CmsSurveyGroupVersion_Type int32

const (
	CmsSurveyGroupVersion_Type_Unspecified CmsSurveyGroupVersion_Type = 0
	CmsSurveyGroupVersion_Lang             CmsSurveyGroupVersion_Type = 2
	CmsSurveyGroupVersion_Location         CmsSurveyGroupVersion_Type = 1
)

// Enum value maps for CmsSurveyGroupVersion_Type.
var (
	CmsSurveyGroupVersion_Type_name = map[int32]string{
		0: "Type_Unspecified",
		2: "Lang",
		1: "Location",
	}
	CmsSurveyGroupVersion_Type_value = map[string]int32{
		"Type_Unspecified": 0,
		"Lang":             2,
		"Location":         1,
	}
)

func (x CmsSurveyGroupVersion_Type) Enum() *CmsSurveyGroupVersion_Type {
	p := new(CmsSurveyGroupVersion_Type)
	*p = x
	return p
}

func (x CmsSurveyGroupVersion_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CmsSurveyGroupVersion_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_cms_survey_group_proto_enumTypes[1].Descriptor()
}

func (CmsSurveyGroupVersion_Type) Type() protoreflect.EnumType {
	return &file_proto_cms_survey_group_proto_enumTypes[1]
}

func (x CmsSurveyGroupVersion_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CmsSurveyGroupVersion_Type.Descriptor instead.
func (CmsSurveyGroupVersion_Type) EnumDescriptor() ([]byte, []int) {
	return file_proto_cms_survey_group_proto_rawDescGZIP(), []int{2, 1}
}

type GroupSurvey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurveyId int64  `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	HashCode string `protobuf:"bytes,2,opt,name=hash_code,json=hashCode,proto3" json:"hash_code,omitempty"`
	Default  bool   `protobuf:"varint,3,opt,name=default,proto3" json:"default,omitempty"`
}

func (x *GroupSurvey) Reset() {
	*x = GroupSurvey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_group_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupSurvey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupSurvey) ProtoMessage() {}

func (x *GroupSurvey) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_group_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupSurvey.ProtoReflect.Descriptor instead.
func (*GroupSurvey) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_group_proto_rawDescGZIP(), []int{0}
}

func (x *GroupSurvey) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *GroupSurvey) GetHashCode() string {
	if x != nil {
		return x.HashCode
	}
	return ""
}

func (x *GroupSurvey) GetDefault() bool {
	if x != nil {
		return x.Default
	}
	return false
}

type GroupSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupSurvey map[string]*GroupSurvey `protobuf:"bytes,1,rep,name=group_survey,json=groupSurvey,proto3" json:"group_survey,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra       *xtype.RawMessage       `protobuf:"bytes,2,opt,name=extra,proto3" json:"extra,omitempty"`
}

func (x *GroupSetting) Reset() {
	*x = GroupSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_group_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupSetting) ProtoMessage() {}

func (x *GroupSetting) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_group_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupSetting.ProtoReflect.Descriptor instead.
func (*GroupSetting) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_group_proto_rawDescGZIP(), []int{1}
}

func (x *GroupSetting) GetGroupSurvey() map[string]*GroupSurvey {
	if x != nil {
		return x.GroupSurvey
	}
	return nil
}

func (x *GroupSetting) GetExtra() *xtype.RawMessage {
	if x != nil {
		return x.Extra
	}
	return nil
}

type CmsSurveyGroupVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64                           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" gorm:"primaryKey"`
	GroupId   int32                           `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Clientid  string                          `protobuf:"bytes,3,opt,name=clientid,proto3" json:"clientid,omitempty"`
	Name      string                          `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	IsPublish int32                           `protobuf:"varint,5,opt,name=is_publish,json=isPublish,proto3" json:"is_publish,omitempty"`
	LimitType CmsSurveyGroupVersion_LimitType `protobuf:"varint,6,opt,name=limit_type,json=limitType,proto3,enum=papegames.sparrow.survey.CmsSurveyGroupVersion_LimitType" json:"limit_type,omitempty"`
	Type      CmsSurveyGroupVersion_Type      `protobuf:"varint,7,opt,name=type,proto3,enum=papegames.sparrow.survey.CmsSurveyGroupVersion_Type" json:"type,omitempty"`
	Settings  *GroupSetting                   `protobuf:"bytes,8,opt,name=settings,proto3" json:"settings,omitempty" gorm:"column:settings;serializer:json"`
	HashCode  string                          `protobuf:"bytes,9,opt,name=hash_code,json=hashCode,proto3" json:"hash_code,omitempty"`
	IsDelete  int32                           `protobuf:"varint,10,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
}

func (x *CmsSurveyGroupVersion) Reset() {
	*x = CmsSurveyGroupVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_survey_group_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyGroupVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyGroupVersion) ProtoMessage() {}

func (x *CmsSurveyGroupVersion) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_survey_group_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyGroupVersion.ProtoReflect.Descriptor instead.
func (*CmsSurveyGroupVersion) Descriptor() ([]byte, []int) {
	return file_proto_cms_survey_group_proto_rawDescGZIP(), []int{2}
}

func (x *CmsSurveyGroupVersion) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CmsSurveyGroupVersion) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *CmsSurveyGroupVersion) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *CmsSurveyGroupVersion) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CmsSurveyGroupVersion) GetIsPublish() int32 {
	if x != nil {
		return x.IsPublish
	}
	return 0
}

func (x *CmsSurveyGroupVersion) GetLimitType() CmsSurveyGroupVersion_LimitType {
	if x != nil {
		return x.LimitType
	}
	return CmsSurveyGroupVersion_LimitType_Unspecified
}

func (x *CmsSurveyGroupVersion) GetType() CmsSurveyGroupVersion_Type {
	if x != nil {
		return x.Type
	}
	return CmsSurveyGroupVersion_Type_Unspecified
}

func (x *CmsSurveyGroupVersion) GetSettings() *GroupSetting {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *CmsSurveyGroupVersion) GetHashCode() string {
	if x != nil {
		return x.HashCode
	}
	return ""
}

func (x *CmsSurveyGroupVersion) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

var File_proto_cms_survey_group_proto protoreflect.FileDescriptor

var file_proto_cms_survey_group_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6d, 0x73, 0x5f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x20, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x72, 0x61, 0x77, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67,
	0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x61, 0x0a, 0x0b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x68,
	0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x68, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x22, 0x83, 0x02, 0x0a, 0x0c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x12, 0x5a, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12,
	0x30, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x1a, 0x65, 0x0a, 0x10, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x81, 0x05, 0x0a, 0x15, 0x43, 0x6d, 0x73,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14,
	0xd2, 0xa7, 0x86, 0x07, 0x0f, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x4b, 0x65, 0x79, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x12, 0x58, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x6d, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x42, 0x29, 0xd2, 0xa7, 0x86, 0x07, 0x24, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x3a, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x3b, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x69, 0x7a, 0x65, 0x72, 0x3a, 0x6a, 0x73, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x73, 0x68, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22,
	0x55, 0x0a, 0x09, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x4f, 0x6e, 0x63, 0x65, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x6f, 0x6c, 0x65, 0x49,
	0x64, 0x4f, 0x6e, 0x63, 0x65, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x6e, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x65, 0x64, 0x10, 0x03, 0x22, 0x34, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x10, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x61, 0x6e, 0x67, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x42, 0x5d, 0x0a, 0x1c,
	0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x2e, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_proto_cms_survey_group_proto_rawDescOnce sync.Once
	file_proto_cms_survey_group_proto_rawDescData = file_proto_cms_survey_group_proto_rawDesc
)

func file_proto_cms_survey_group_proto_rawDescGZIP() []byte {
	file_proto_cms_survey_group_proto_rawDescOnce.Do(func() {
		file_proto_cms_survey_group_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_cms_survey_group_proto_rawDescData)
	})
	return file_proto_cms_survey_group_proto_rawDescData
}

var file_proto_cms_survey_group_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_cms_survey_group_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proto_cms_survey_group_proto_goTypes = []any{
	(CmsSurveyGroupVersion_LimitType)(0), // 0: papegames.sparrow.survey.CmsSurveyGroupVersion.LimitType
	(CmsSurveyGroupVersion_Type)(0),      // 1: papegames.sparrow.survey.CmsSurveyGroupVersion.Type
	(*GroupSurvey)(nil),                  // 2: papegames.sparrow.survey.GroupSurvey
	(*GroupSetting)(nil),                 // 3: papegames.sparrow.survey.GroupSetting
	(*CmsSurveyGroupVersion)(nil),        // 4: papegames.sparrow.survey.CmsSurveyGroupVersion
	nil,                                  // 5: papegames.sparrow.survey.GroupSetting.GroupSurveyEntry
	(*xtype.RawMessage)(nil),             // 6: papegames.type.RawMessage
}
var file_proto_cms_survey_group_proto_depIdxs = []int32{
	5, // 0: papegames.sparrow.survey.GroupSetting.group_survey:type_name -> papegames.sparrow.survey.GroupSetting.GroupSurveyEntry
	6, // 1: papegames.sparrow.survey.GroupSetting.extra:type_name -> papegames.type.RawMessage
	0, // 2: papegames.sparrow.survey.CmsSurveyGroupVersion.limit_type:type_name -> papegames.sparrow.survey.CmsSurveyGroupVersion.LimitType
	1, // 3: papegames.sparrow.survey.CmsSurveyGroupVersion.type:type_name -> papegames.sparrow.survey.CmsSurveyGroupVersion.Type
	3, // 4: papegames.sparrow.survey.CmsSurveyGroupVersion.settings:type_name -> papegames.sparrow.survey.GroupSetting
	2, // 5: papegames.sparrow.survey.GroupSetting.GroupSurveyEntry.value:type_name -> papegames.sparrow.survey.GroupSurvey
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_proto_cms_survey_group_proto_init() }
func file_proto_cms_survey_group_proto_init() {
	if File_proto_cms_survey_group_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_cms_survey_group_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GroupSurvey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_group_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GroupSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_survey_group_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyGroupVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_cms_survey_group_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_cms_survey_group_proto_goTypes,
		DependencyIndexes: file_proto_cms_survey_group_proto_depIdxs,
		EnumInfos:         file_proto_cms_survey_group_proto_enumTypes,
		MessageInfos:      file_proto_cms_survey_group_proto_msgTypes,
	}.Build()
	File_proto_cms_survey_group_proto = out.File
	file_proto_cms_survey_group_proto_rawDesc = nil
	file_proto_cms_survey_group_proto_goTypes = nil
	file_proto_cms_survey_group_proto_depIdxs = nil
}
