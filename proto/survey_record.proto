syntax = "proto3";

package papegames.sparrow.survey;

import "papegames/type/timestamp.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto";

option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

// SurveyRecord
message SurveyRecord {
  // The suvery record id
  int32 id = 1 [
    (tagger.tags) = "gorm:primaryKey"
  ];
  // The survey record begin time
  papegames.type.Timestamp begin_time = 4;
  // The survey record end time
  papegames.type.Timestamp end_time = 5;
  // The survey record create time
  papegames.type.Timestamp ctime = 6;

  string role_id = 7 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "gorm:column:role_id",
    (tagger.tags) = "json:role_id"
  ];

  string openid = 8 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "gorm:column:openid",
    (tagger.tags) = "json:openid"
  ];

  string device_id = 9 [
    (openapi.v3.property) = {
      max_length: 200,
    },
    (tagger.tags) = "gorm:column:device_id",
    (tagger.tags) = "json:device_id"
  ];

  string ip = 10 [
    (openapi.v3.property) = {
      max_length: 200,
    },
    (tagger.tags) = "gorm:column:ip",
    (tagger.tags) = "json:ip"
  ];

  int32 is_valid = 11 [
    (tagger.tags) = "gorm:column:is_valid",
    (tagger.tags) = "json:is_valid"
  ];

  string extra = 12 [
    (tagger.tags) = "gorm:column:extra",
    (tagger.tags) = "json:extra"
  ];
}

// SurveyRecordDetail
message SurveyRecordDetail {
  // The suvery record detail id
  int32 id = 1 [
    (tagger.tags) = "gorm:primaryKey"
  ];

  // The survey record detail question
  string question = 4 [
    (openapi.v3.property) = {
      min_length: 6,
      max_length: 64,
    },
    (tagger.tags) = "gorm:size(64)"
  ];
  // The survey record detail option
  string option = 5 [
    (openapi.v3.property) = {
      min_length: 6,
      max_length: 64,
    },
    (tagger.tags) = "gorm:size(64)"
  ];
  // The survey record detail text
  string text = 6 [
    (openapi.v3.property) = {
      max_length: 256,
    },
    (tagger.tags) = "gorm:size(256)"
  ];

  int32 survey_record_id = 7 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "gorm:column:survey_record_id",
    (tagger.tags) = "json:survey_record_id"
  ];
}
