// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_strategy_record.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SurveyStrategyRecord
type SurveyStrategyRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The suvery strategy record id
	Clientid   string `protobuf:"bytes,1,opt,name=clientid,proto3" json:"clientid,omitempty" gorm:"primaryKey"`
	StrategyId int64  `protobuf:"varint,2,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id" gorm:"primaryKey;column:strategy_id"`
	Openid     string `protobuf:"bytes,3,opt,name=openid,proto3" json:"openid" gorm:"primaryKey;column:openid"`
	RoleId     string `protobuf:"bytes,4,opt,name=role_id,json=roleId,proto3" json:"role_id" gorm:"primaryKey;column:role_id"`
	SurveyId   string `protobuf:"bytes,5,opt,name=survey_id,json=surveyId,proto3" json:"survey_id" gorm:"column:survey_id"`
	// The survey record create time
	Ctime *xtype.Timestamp `protobuf:"bytes,6,opt,name=ctime,proto3" json:"ctime,omitempty"`
}

func (x *SurveyStrategyRecord) Reset() {
	*x = SurveyStrategyRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_strategy_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyStrategyRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyStrategyRecord) ProtoMessage() {}

func (x *SurveyStrategyRecord) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_strategy_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyStrategyRecord.ProtoReflect.Descriptor instead.
func (*SurveyStrategyRecord) Descriptor() ([]byte, []int) {
	return file_proto_survey_strategy_record_proto_rawDescGZIP(), []int{0}
}

func (x *SurveyStrategyRecord) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *SurveyStrategyRecord) GetStrategyId() int64 {
	if x != nil {
		return x.StrategyId
	}
	return 0
}

func (x *SurveyStrategyRecord) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *SurveyStrategyRecord) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *SurveyStrategyRecord) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

func (x *SurveyStrategyRecord) GetCtime() *xtype.Timestamp {
	if x != nil {
		return x.Ctime
	}
	return nil
}

var File_proto_survey_strategy_record_proto protoreflect.FileDescriptor

var file_proto_survey_strategy_record_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x1e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67,
	0x67, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xce, 0x03, 0x0a, 0x14, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x30, 0x0a, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0xd2, 0xa7, 0x86,
	0x07, 0x0f, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x12, 0x5d, 0x0a, 0x0b, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x3c, 0xd2, 0xa7, 0x86, 0x07, 0x22, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x3b, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x10, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x69, 0x64, 0x52, 0x0a,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x37, 0xba, 0x47, 0x02, 0x78,
	0x40, 0xd2, 0xa7, 0x86, 0x07, 0x1d, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x4b, 0x65, 0x79, 0x3b, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x6f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x52, 0x0a, 0x07, 0x72,
	0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0xba, 0x47,
	0x02, 0x78, 0x40, 0xd2, 0xa7, 0x86, 0x07, 0x1e, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x3b, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x72,
	0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x4f, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x32, 0xba, 0x47, 0x02, 0x78, 0x40, 0xd2, 0xa7, 0x86, 0x07, 0x15, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x5f, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d,
	0x65, 0x42, 0x5d, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01,
	0x5a, 0x2e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_strategy_record_proto_rawDescOnce sync.Once
	file_proto_survey_strategy_record_proto_rawDescData = file_proto_survey_strategy_record_proto_rawDesc
)

func file_proto_survey_strategy_record_proto_rawDescGZIP() []byte {
	file_proto_survey_strategy_record_proto_rawDescOnce.Do(func() {
		file_proto_survey_strategy_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_strategy_record_proto_rawDescData)
	})
	return file_proto_survey_strategy_record_proto_rawDescData
}

var file_proto_survey_strategy_record_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_survey_strategy_record_proto_goTypes = []any{
	(*SurveyStrategyRecord)(nil), // 0: papegames.sparrow.survey.SurveyStrategyRecord
	(*xtype.Timestamp)(nil),      // 1: papegames.type.Timestamp
}
var file_proto_survey_strategy_record_proto_depIdxs = []int32{
	1, // 0: papegames.sparrow.survey.SurveyStrategyRecord.ctime:type_name -> papegames.type.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_survey_strategy_record_proto_init() }
func file_proto_survey_strategy_record_proto_init() {
	if File_proto_survey_strategy_record_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_strategy_record_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyStrategyRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_strategy_record_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_strategy_record_proto_goTypes,
		DependencyIndexes: file_proto_survey_strategy_record_proto_depIdxs,
		MessageInfos:      file_proto_survey_strategy_record_proto_msgTypes,
	}.Build()
	File_proto_survey_strategy_record_proto = out.File
	file_proto_survey_strategy_record_proto_rawDesc = nil
	file_proto_survey_strategy_record_proto_goTypes = nil
	file_proto_survey_strategy_record_proto_depIdxs = nil
}
