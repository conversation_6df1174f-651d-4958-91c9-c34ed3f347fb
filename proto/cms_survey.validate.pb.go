// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/cms_survey.proto

package proto

func (x *CmsSurveyVersion) Validate() error {
	if v, ok := interface{}(x.GetSettingStruct()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersionValidationError{
				field:   "SettingStruct",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *CmsSurveyVersion_Setting) Validate() error {
	if v, ok := interface{}(x.GetBaseRuleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersion_SettingValidationError{
				field:   "BaseRuleConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAnswerLimitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersion_SettingValidationError{
				field:   "AnswerLimitConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetGiftConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersion_SettingValidationError{
				field:   "GiftConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *CmsSurveyVersion_WebSettings) Validate() error {
	return nil
}

func (x *CmsSurveyVersion_AnswerLimitConfig) Validate() error {
	return nil
}

func (x *CmsSurveyVersion_BaseRuleConfig) Validate() error {
	if v, ok := interface{}(x.GetAnswerTimesConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersion_BaseRuleConfigValidationError{
				field:   "AnswerTimesConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetPeriodicControl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersion_BaseRuleConfigValidationError{
				field:   "PeriodicControl",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *CmsSurveyVersion_AnswerTimesConfig) Validate() error {
	return nil
}

func (x *CmsSurveyVersion_PeriodicControl) Validate() error {
	return nil
}

func (x *CmsSurveyVersion_GiftConfig) Validate() error {
	if v, ok := interface{}(x.GetPreAwardConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersion_GiftConfigValidationError{
				field:   "PreAwardConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetRedeemConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersion_GiftConfigValidationError{
				field:   "RedeemConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetPushAwardConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyVersion_GiftConfigValidationError{
				field:   "PushAwardConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *CmsSurveyVersion_PreAwardConfig) Validate() error {
	return nil
}

func (x *CmsSurveyVersion_RedeemConfig) Validate() error {
	return nil
}

func (x *CmsSurveyVersion_PushAwardConfig) Validate() error {
	return nil
}

type CmsSurveyVersionValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersionValidationError) Field() string { return e.field }

func (e CmsSurveyVersionValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersionValidationError) Message() string { return e.message }

func (e CmsSurveyVersionValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersionValidationError) ErrorName() string { return "CmsSurveyVersionValidationError" }

func (e CmsSurveyVersionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_SettingValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_SettingValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_SettingValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_SettingValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_SettingValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_SettingValidationError) ErrorName() string {
	return "CmsSurveyVersion_SettingValidationError"
}

func (e CmsSurveyVersion_SettingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_Setting." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_WebSettingsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_WebSettingsValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_WebSettingsValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_WebSettingsValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_WebSettingsValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_WebSettingsValidationError) ErrorName() string {
	return "CmsSurveyVersion_WebSettingsValidationError"
}

func (e CmsSurveyVersion_WebSettingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_WebSettings." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_AnswerLimitConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_AnswerLimitConfigValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_AnswerLimitConfigValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_AnswerLimitConfigValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_AnswerLimitConfigValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_AnswerLimitConfigValidationError) ErrorName() string {
	return "CmsSurveyVersion_AnswerLimitConfigValidationError"
}

func (e CmsSurveyVersion_AnswerLimitConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_AnswerLimitConfig." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_BaseRuleConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_BaseRuleConfigValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_BaseRuleConfigValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_BaseRuleConfigValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_BaseRuleConfigValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_BaseRuleConfigValidationError) ErrorName() string {
	return "CmsSurveyVersion_BaseRuleConfigValidationError"
}

func (e CmsSurveyVersion_BaseRuleConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_BaseRuleConfig." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_AnswerTimesConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_AnswerTimesConfigValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_AnswerTimesConfigValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_AnswerTimesConfigValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_AnswerTimesConfigValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_AnswerTimesConfigValidationError) ErrorName() string {
	return "CmsSurveyVersion_AnswerTimesConfigValidationError"
}

func (e CmsSurveyVersion_AnswerTimesConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_AnswerTimesConfig." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_PeriodicControlValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_PeriodicControlValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_PeriodicControlValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_PeriodicControlValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_PeriodicControlValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_PeriodicControlValidationError) ErrorName() string {
	return "CmsSurveyVersion_PeriodicControlValidationError"
}

func (e CmsSurveyVersion_PeriodicControlValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_PeriodicControl." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_GiftConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_GiftConfigValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_GiftConfigValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_GiftConfigValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_GiftConfigValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_GiftConfigValidationError) ErrorName() string {
	return "CmsSurveyVersion_GiftConfigValidationError"
}

func (e CmsSurveyVersion_GiftConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_GiftConfig." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_PreAwardConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_PreAwardConfigValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_PreAwardConfigValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_PreAwardConfigValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_PreAwardConfigValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_PreAwardConfigValidationError) ErrorName() string {
	return "CmsSurveyVersion_PreAwardConfigValidationError"
}

func (e CmsSurveyVersion_PreAwardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_PreAwardConfig." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_RedeemConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_RedeemConfigValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_RedeemConfigValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_RedeemConfigValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_RedeemConfigValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_RedeemConfigValidationError) ErrorName() string {
	return "CmsSurveyVersion_RedeemConfigValidationError"
}

func (e CmsSurveyVersion_RedeemConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_RedeemConfig." + e.field + ": " + e.message + cause
}

type CmsSurveyVersion_PushAwardConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyVersion_PushAwardConfigValidationError) Field() string { return e.field }

func (e CmsSurveyVersion_PushAwardConfigValidationError) Reason() string { return e.reason }

func (e CmsSurveyVersion_PushAwardConfigValidationError) Message() string { return e.message }

func (e CmsSurveyVersion_PushAwardConfigValidationError) Cause() error { return e.cause }

func (e CmsSurveyVersion_PushAwardConfigValidationError) ErrorName() string {
	return "CmsSurveyVersion_PushAwardConfigValidationError"
}

func (e CmsSurveyVersion_PushAwardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyVersion_PushAwardConfig." + e.field + ": " + e.message + cause
}
