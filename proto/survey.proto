syntax = "proto3";

package papegames.sparrow.survey;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto";
import "papegames/type/empty.proto";
import "papegames/type/routing.proto";
import "papegames/type/raw_message.proto";


option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";
// 管理端
service SurveyServiceAdmin{
  option (google.api.default_host) = "survey-graph.papegames.com";
  // 配置重载
  rpc ConfigReload(ConfigReloadRequest) returns (papegames.type.Empty) {
    option (papegames.type.method_routing).path = "/v1/survey/internal";
    option (papegames.type.method_routing).name = "internal";
    option (google.api.http) = {
      post: "/v1/survey/internal/config/reload"
    };
  }
}

// 问卷 
service SurveyService {
  option (google.api.default_host) = "survey-graph.papegames.com";

  // 问卷提交
  // 角色或者账号登录需验证authority
  rpc SubmitSurvey(SubmitSurveyRequest) returns (SubmitSurveyResponse) {
    option (google.api.http) = {
      post: "/v1/survey/submit"
      body: "body"
    };
  }

  // 登陆 
  rpc Signin(SigninRequest) returns (SigninResponse) {
    option (google.api.http) = {
      post: "/v1/survey/signin"
    };
  }

  // 获取配置
  rpc Config(ConfigRequest) returns (ConfigResponse) {
    option (google.api.http) = {
      get: "/v1/survey/config"
    };
  }

  // 获取问卷答题信息
  // 角色或者账号登录需验证authority
  rpc Answer(AnswerRequest) returns (AnswerResponse) {
    option (google.api.http) = {
      get: "/v1/survey/answer"
    };
  }


  // 获取问卷组配置
  rpc GroupConfig(GroupConfigRequest) returns (GroupConfigResponse) {
    option (google.api.http) = {
      get: "/v1/survey/group/config"
    };
  }
  
  // 健康检测
  rpc Health(HealthRequest) returns (papegames.type.Empty) {                                           
    option (google.api.http) = {                                                                       
      get: "/v1/survey/health"                                                                       
    };                                                                                               
  }
}

message HealthRequest{}

// Request message for SurveyService.SubmitSurvey
message SubmitSurveyRequest {
  // 问卷ID 
  string survey_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      min_length: 10,
      max_length: 14,
    }
  ];

  // 问卷内容(经snappy压缩),eg. 
  //  {
  //  	"survey_id": "xxxxx",
  //    "openid" : "",
  //    "role_id" : "",
  //    "device_id" : "",
  //  	"begin_time": "2022-01-02T09:13:47+08:00",
  //  	"end_time": "2022-01-02T09:13:47+08:00",
  //  	"survey_records": {
  //  		"hello world": [{
  //  			"option": "A",
  //  			"text": "yes"
  //  		}]
  //  	},
  //  	"extra": "xxxx",
  //    "flag": 1
  //  }
  bytes body = 2 [
    (google.api.field_behavior) = REQUIRED
  ];

  // 时区
  string timezone = 3;
  // 问卷组
  string group = 4;
  // 渠道id
  string platid = 5;
}

// Response message for SurveyService.SubmitSurvey
message SubmitSurveyResponse {
  // 发奖信息 
  AwardInfo award_info = 1;
  // 签名字段
  string sign = 2;
}

// 发奖信息
message AwardInfo {
  // 形式, 0: 邮件 1: 兑换码
  string type = 1 [
    (tagger.tags) = "json:type,omitempty"
  ];
  // 值,形式为兑换码时表示兑换码
  string value = 2 [
    (tagger.tags) = "json:value,omitempty"
  ];

  // 问卷记录ID
  int32 survey_record_id = 3;
}

message AnswerRequest {
  // 问卷ID 
  string survey_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      min_length: 10,
      max_length: 14,
    }
  ];

  // 用户ID
  string openid = 2 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "json:openid"
  ];

  // 角色ID
  string role_id = 3 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "json:role_id"
  ];

  // 设备ID
  string device_id = 4 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "json:device_id"
  ];

  string lang = 5;
}

// Response message for SurveyService.Answer
message AnswerResponse {
  // 问卷答题信息
	// {
	//		"hello world": [{
	//			"option": "A1",
	//			"text": "yes"
	//		}, {
	//			"option": "B1",
	//			"text": "no"
	//		}],
	//		"hello world2": [{
	//			"option": "A2",
	//			"text": "1"
	//		}, {
	//			"option": "B2",
	//			"text": "2"
	//		}]
	// }
  papegames.type.RawMessage survey_records = 1;
  // schema配置
  papegames.type.RawMessage schema = 2;
  // materials config
  papegames.type.RawMessage materials_config = 3;
  // web_settings
  papegames.type.RawMessage web_settings = 4;

  string name = 5; 
}


// Request message for SurveyService.Signin
message SigninRequest {
  // 用户Token
  string token = 1 [
    (openapi.v3.property) = {
      max_length: 1024,
    }
  ];

  // 问卷ID 
  string survey_id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      min_length: 10,
      max_length: 14,
    }
  ];

  // 用户ID
  string openid = 3 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "json:openid"
  ];

  // 角色ID
  string role_id = 4 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "json:role_id"
  ];

  // 设备ID
  string device_id = 5 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "json:device_id"
  ];

  // 时区
  string timezone = 6;
  // 问卷组
  string group = 7;
}

// Response message for SurveyService.Signin
message SigninResponse {
  // The authority, x-Authority
  string authority = 1;

  // 发奖信息 错误码1004时有值
  AwardInfo award_info = 2; 
  // 签名字段 错误码1004时有值
  string sign = 3;
}

// Request message for SurveyService.Config
message ConfigRequest {
  // 问卷ID
  string survey_id = 1 [
    (google.api.field_behavior) = REQUIRED
  ];

  string lang = 2;
}


// Response message for SurveyService.Config
message ConfigResponse {
  option (tagger.disable_omitempty) = true;

  // schema配置
    papegames.type.RawMessage schema = 1;
    // materials config
    papegames.type.RawMessage materials_config = 2;
    // web_settings
    papegames.type.RawMessage web_settings = 3;

    // name
    string name = 4;
    papegames.type.RawMessage font = 5;
}


// Request message for SurveyInternalService.ConfigReload
message ConfigReloadRequest {
  // 问卷ID
  string survey_id = 1 ;
  // 问卷组ID
  string group = 2;
}

// 获取问卷组 问卷内容
message GroupConfigRequest {
  // 问卷ID
  string group = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string lang = 2;
}

message GroupConfigResponse {
  option (tagger.disable_omitempty) = true;
  //问卷组
  string group = 6;
  // 问卷id
  string survey_id = 7;
}
