// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey.proto

package proto

func (x *HealthRequest) Validate() error {
	return nil
}

func (x *SubmitSurveyRequest) Validate() error {
	if len(x.GetSurveyId()) == 0 {
		return SubmitSurveyRequestValidationError{
			field:   "SurveyId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSurveyId()) < 10 {
		return SubmitSurveyRequestValidationError{
			field:   "SurveyId",
			reason:  "min_length",
			message: "value must be at least 10 bytes",
		}
	}
	if len(x.GetSurveyId()) > 14 {
		return SubmitSurveyRequestValidationError{
			field:   "SurveyId",
			reason:  "max_length",
			message: "value length must be at most 14 bytes",
		}
	}
	return nil
}

func (x *SubmitSurveyResponse) Validate() error {
	if v, ok := interface{}(x.GetAwardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitSurveyResponseValidationError{
				field:   "AwardInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *AwardInfo) Validate() error {
	return nil
}

func (x *AnswerRequest) Validate() error {
	if len(x.GetSurveyId()) == 0 {
		return AnswerRequestValidationError{
			field:   "SurveyId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSurveyId()) < 10 {
		return AnswerRequestValidationError{
			field:   "SurveyId",
			reason:  "min_length",
			message: "value must be at least 10 bytes",
		}
	}
	if len(x.GetSurveyId()) > 14 {
		return AnswerRequestValidationError{
			field:   "SurveyId",
			reason:  "max_length",
			message: "value length must be at most 14 bytes",
		}
	}
	if len(x.GetOpenid()) > 128 {
		return AnswerRequestValidationError{
			field:   "Openid",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.GetRoleId()) > 128 {
		return AnswerRequestValidationError{
			field:   "RoleId",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.GetDeviceId()) > 128 {
		return AnswerRequestValidationError{
			field:   "DeviceId",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	return nil
}

func (x *AnswerResponse) Validate() error {
	return nil
}

func (x *SigninRequest) Validate() error {
	if len(x.GetToken()) > 1024 {
		return SigninRequestValidationError{
			field:   "Token",
			reason:  "max_length",
			message: "value length must be at most 1024 bytes",
		}
	}
	if len(x.GetSurveyId()) == 0 {
		return SigninRequestValidationError{
			field:   "SurveyId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSurveyId()) < 10 {
		return SigninRequestValidationError{
			field:   "SurveyId",
			reason:  "min_length",
			message: "value must be at least 10 bytes",
		}
	}
	if len(x.GetSurveyId()) > 14 {
		return SigninRequestValidationError{
			field:   "SurveyId",
			reason:  "max_length",
			message: "value length must be at most 14 bytes",
		}
	}
	if len(x.GetOpenid()) > 128 {
		return SigninRequestValidationError{
			field:   "Openid",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.GetRoleId()) > 128 {
		return SigninRequestValidationError{
			field:   "RoleId",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.GetDeviceId()) > 128 {
		return SigninRequestValidationError{
			field:   "DeviceId",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	return nil
}

func (x *SigninResponse) Validate() error {
	if v, ok := interface{}(x.GetAwardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SigninResponseValidationError{
				field:   "AwardInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ConfigRequest) Validate() error {
	if len(x.GetSurveyId()) == 0 {
		return ConfigRequestValidationError{
			field:   "SurveyId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *ConfigResponse) Validate() error {
	return nil
}

func (x *ConfigReloadRequest) Validate() error {
	return nil
}

func (x *GroupConfigRequest) Validate() error {
	if len(x.GetGroup()) == 0 {
		return GroupConfigRequestValidationError{
			field:   "Group",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *GroupConfigResponse) Validate() error {
	return nil
}

type HealthRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e HealthRequestValidationError) Field() string { return e.field }

func (e HealthRequestValidationError) Reason() string { return e.reason }

func (e HealthRequestValidationError) Message() string { return e.message }

func (e HealthRequestValidationError) Cause() error { return e.cause }

func (e HealthRequestValidationError) ErrorName() string { return "HealthRequestValidationError" }

func (e HealthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid HealthRequest." + e.field + ": " + e.message + cause
}

type SubmitSurveyRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SubmitSurveyRequestValidationError) Field() string { return e.field }

func (e SubmitSurveyRequestValidationError) Reason() string { return e.reason }

func (e SubmitSurveyRequestValidationError) Message() string { return e.message }

func (e SubmitSurveyRequestValidationError) Cause() error { return e.cause }

func (e SubmitSurveyRequestValidationError) ErrorName() string {
	return "SubmitSurveyRequestValidationError"
}

func (e SubmitSurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SubmitSurveyRequest." + e.field + ": " + e.message + cause
}

type SubmitSurveyResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SubmitSurveyResponseValidationError) Field() string { return e.field }

func (e SubmitSurveyResponseValidationError) Reason() string { return e.reason }

func (e SubmitSurveyResponseValidationError) Message() string { return e.message }

func (e SubmitSurveyResponseValidationError) Cause() error { return e.cause }

func (e SubmitSurveyResponseValidationError) ErrorName() string {
	return "SubmitSurveyResponseValidationError"
}

func (e SubmitSurveyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SubmitSurveyResponse." + e.field + ": " + e.message + cause
}

type AwardInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AwardInfoValidationError) Field() string { return e.field }

func (e AwardInfoValidationError) Reason() string { return e.reason }

func (e AwardInfoValidationError) Message() string { return e.message }

func (e AwardInfoValidationError) Cause() error { return e.cause }

func (e AwardInfoValidationError) ErrorName() string { return "AwardInfoValidationError" }

func (e AwardInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AwardInfo." + e.field + ": " + e.message + cause
}

type AnswerRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AnswerRequestValidationError) Field() string { return e.field }

func (e AnswerRequestValidationError) Reason() string { return e.reason }

func (e AnswerRequestValidationError) Message() string { return e.message }

func (e AnswerRequestValidationError) Cause() error { return e.cause }

func (e AnswerRequestValidationError) ErrorName() string { return "AnswerRequestValidationError" }

func (e AnswerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AnswerRequest." + e.field + ": " + e.message + cause
}

type AnswerResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AnswerResponseValidationError) Field() string { return e.field }

func (e AnswerResponseValidationError) Reason() string { return e.reason }

func (e AnswerResponseValidationError) Message() string { return e.message }

func (e AnswerResponseValidationError) Cause() error { return e.cause }

func (e AnswerResponseValidationError) ErrorName() string { return "AnswerResponseValidationError" }

func (e AnswerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AnswerResponse." + e.field + ": " + e.message + cause
}

type SigninRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SigninRequestValidationError) Field() string { return e.field }

func (e SigninRequestValidationError) Reason() string { return e.reason }

func (e SigninRequestValidationError) Message() string { return e.message }

func (e SigninRequestValidationError) Cause() error { return e.cause }

func (e SigninRequestValidationError) ErrorName() string { return "SigninRequestValidationError" }

func (e SigninRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SigninRequest." + e.field + ": " + e.message + cause
}

type SigninResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SigninResponseValidationError) Field() string { return e.field }

func (e SigninResponseValidationError) Reason() string { return e.reason }

func (e SigninResponseValidationError) Message() string { return e.message }

func (e SigninResponseValidationError) Cause() error { return e.cause }

func (e SigninResponseValidationError) ErrorName() string { return "SigninResponseValidationError" }

func (e SigninResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SigninResponse." + e.field + ": " + e.message + cause
}

type ConfigRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ConfigRequestValidationError) Field() string { return e.field }

func (e ConfigRequestValidationError) Reason() string { return e.reason }

func (e ConfigRequestValidationError) Message() string { return e.message }

func (e ConfigRequestValidationError) Cause() error { return e.cause }

func (e ConfigRequestValidationError) ErrorName() string { return "ConfigRequestValidationError" }

func (e ConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ConfigRequest." + e.field + ": " + e.message + cause
}

type ConfigResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ConfigResponseValidationError) Field() string { return e.field }

func (e ConfigResponseValidationError) Reason() string { return e.reason }

func (e ConfigResponseValidationError) Message() string { return e.message }

func (e ConfigResponseValidationError) Cause() error { return e.cause }

func (e ConfigResponseValidationError) ErrorName() string { return "ConfigResponseValidationError" }

func (e ConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ConfigResponse." + e.field + ": " + e.message + cause
}

type ConfigReloadRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ConfigReloadRequestValidationError) Field() string { return e.field }

func (e ConfigReloadRequestValidationError) Reason() string { return e.reason }

func (e ConfigReloadRequestValidationError) Message() string { return e.message }

func (e ConfigReloadRequestValidationError) Cause() error { return e.cause }

func (e ConfigReloadRequestValidationError) ErrorName() string {
	return "ConfigReloadRequestValidationError"
}

func (e ConfigReloadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ConfigReloadRequest." + e.field + ": " + e.message + cause
}

type GroupConfigRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GroupConfigRequestValidationError) Field() string { return e.field }

func (e GroupConfigRequestValidationError) Reason() string { return e.reason }

func (e GroupConfigRequestValidationError) Message() string { return e.message }

func (e GroupConfigRequestValidationError) Cause() error { return e.cause }

func (e GroupConfigRequestValidationError) ErrorName() string {
	return "GroupConfigRequestValidationError"
}

func (e GroupConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GroupConfigRequest." + e.field + ": " + e.message + cause
}

type GroupConfigResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GroupConfigResponseValidationError) Field() string { return e.field }

func (e GroupConfigResponseValidationError) Reason() string { return e.reason }

func (e GroupConfigResponseValidationError) Message() string { return e.message }

func (e GroupConfigResponseValidationError) Cause() error { return e.cause }

func (e GroupConfigResponseValidationError) ErrorName() string {
	return "GroupConfigResponseValidationError"
}

func (e GroupConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GroupConfigResponse." + e.field + ": " + e.message + cause
}
