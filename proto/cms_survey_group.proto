syntax = "proto3";

package papegames.sparrow.survey;

import "papegames/type/raw_message.proto";
import "tagger/tagger.proto";

option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message GroupSurvey {
    int64 survey_id = 1;
    string hash_code = 2;
    bool default = 3;
}
message GroupSetting { 
    map<string,GroupSurvey> group_survey = 1;  
    papegames.type.RawMessage extra = 2;
}

message CmsSurveyGroupVersion { 
    enum LimitType {
        LimitType_Unspecified = 0;
        OpenIdOnce = 1;
        RoleIdOnce = 2;
        Unlimited = 3;
    }
    enum Type {
        Type_Unspecified = 0;
        Lang = 2;
        Location = 1;
    }
    int64 id = 1 [
      (tagger.tags) = "gorm:primaryKey"
    ];
    int32 group_id = 2;
    string clientid = 3;
    string name = 4;  
    int32 is_publish = 5;
    LimitType limit_type = 6;
    Type type = 7;
    GroupSetting settings = 8[
         (tagger.tags) = "gorm:column:settings;serializer:json"
    ];
    string hash_code = 9;
    int32 is_delete = 10;
}