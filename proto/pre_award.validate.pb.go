// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/pre_award.proto

package proto

func (x *PreAward) Validate() error {
	return nil
}

type PreAwardValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PreAwardValidationError) Field() string { return e.field }

func (e PreAwardValidationError) Reason() string { return e.reason }

func (e PreAwardValidationError) Message() string { return e.message }

func (e PreAwardValidationError) Cause() error { return e.cause }

func (e PreAwardValidationError) ErrorName() string { return "PreAwardValidationError" }

func (e PreAwardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PreAward." + e.field + ": " + e.message + cause
}
