syntax = "proto3";

package papegames.sparrow.survey;

import "papegames/type/timestamp.proto";

option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message PreAwardTemplate {
  int64 id = 1;

  string name = 2;

  string clientid = 3;

  string sender = 4;

  string title = 5;

  string body = 6;

  string content = 7;

  papegames.type.Timestamp stime = 8;

  papegames.type.Timestamp etime = 9;

  int32 permanent = 10;
}
