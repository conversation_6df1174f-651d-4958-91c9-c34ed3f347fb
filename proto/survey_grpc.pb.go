// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v4.25.1
// source: proto/survey.proto

package proto

import (
	context "context"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	SurveyServiceAdmin_ConfigReload_FullMethodName = "/papegames.sparrow.survey.SurveyServiceAdmin/ConfigReload"
)

// SurveyServiceAdminClient is the client API for SurveyServiceAdmin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 管理端
type SurveyServiceAdminClient interface {
	// 配置重载
	ConfigReload(ctx context.Context, in *ConfigReloadRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
}

type surveyServiceAdminClient struct {
	cc grpc.ClientConnInterface
}

func NewSurveyServiceAdminClient(cc grpc.ClientConnInterface) SurveyServiceAdminClient {
	return &surveyServiceAdminClient{cc}
}

func (c *surveyServiceAdminClient) ConfigReload(ctx context.Context, in *ConfigReloadRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyServiceAdmin_ConfigReload_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SurveyServiceAdminServer is the server API for SurveyServiceAdmin service.
// All implementations must embed UnimplementedSurveyServiceAdminServer
// for forward compatibility
//
// 管理端
type SurveyServiceAdminServer interface {
	// 配置重载
	ConfigReload(context.Context, *ConfigReloadRequest) (*xtype.Empty, error)
	mustEmbedUnimplementedSurveyServiceAdminServer()
}

// UnimplementedSurveyServiceAdminServer must be embedded to have forward compatible implementations.
type UnimplementedSurveyServiceAdminServer struct {
}

func (UnimplementedSurveyServiceAdminServer) ConfigReload(context.Context, *ConfigReloadRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfigReload not implemented")
}
func (UnimplementedSurveyServiceAdminServer) mustEmbedUnimplementedSurveyServiceAdminServer() {}

// UnsafeSurveyServiceAdminServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SurveyServiceAdminServer will
// result in compilation errors.
type UnsafeSurveyServiceAdminServer interface {
	mustEmbedUnimplementedSurveyServiceAdminServer()
}

func RegisterSurveyServiceAdminServer(s grpc.ServiceRegistrar, srv SurveyServiceAdminServer) {
	s.RegisterService(&SurveyServiceAdmin_ServiceDesc, srv)
}

func _SurveyServiceAdmin_ConfigReload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigReloadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceAdminServer).ConfigReload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyServiceAdmin_ConfigReload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceAdminServer).ConfigReload(ctx, req.(*ConfigReloadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SurveyServiceAdmin_ServiceDesc is the grpc.ServiceDesc for SurveyServiceAdmin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SurveyServiceAdmin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "papegames.sparrow.survey.SurveyServiceAdmin",
	HandlerType: (*SurveyServiceAdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ConfigReload",
			Handler:    _SurveyServiceAdmin_ConfigReload_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/survey.proto",
}

const (
	SurveyService_SubmitSurvey_FullMethodName = "/papegames.sparrow.survey.SurveyService/SubmitSurvey"
	SurveyService_Signin_FullMethodName       = "/papegames.sparrow.survey.SurveyService/Signin"
	SurveyService_Config_FullMethodName       = "/papegames.sparrow.survey.SurveyService/Config"
	SurveyService_Answer_FullMethodName       = "/papegames.sparrow.survey.SurveyService/Answer"
	SurveyService_GroupConfig_FullMethodName  = "/papegames.sparrow.survey.SurveyService/GroupConfig"
	SurveyService_Health_FullMethodName       = "/papegames.sparrow.survey.SurveyService/Health"
)

// SurveyServiceClient is the client API for SurveyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 问卷
type SurveyServiceClient interface {
	// 问卷提交
	// 角色或者账号登录需验证authority
	SubmitSurvey(ctx context.Context, in *SubmitSurveyRequest, opts ...grpc.CallOption) (*SubmitSurveyResponse, error)
	// 登陆
	Signin(ctx context.Context, in *SigninRequest, opts ...grpc.CallOption) (*SigninResponse, error)
	// 获取配置
	Config(ctx context.Context, in *ConfigRequest, opts ...grpc.CallOption) (*ConfigResponse, error)
	// 获取问卷答题信息
	// 角色或者账号登录需验证authority
	Answer(ctx context.Context, in *AnswerRequest, opts ...grpc.CallOption) (*AnswerResponse, error)
	// 获取问卷组配置
	GroupConfig(ctx context.Context, in *GroupConfigRequest, opts ...grpc.CallOption) (*GroupConfigResponse, error)
	// 健康检测
	Health(ctx context.Context, in *HealthRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
}

type surveyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSurveyServiceClient(cc grpc.ClientConnInterface) SurveyServiceClient {
	return &surveyServiceClient{cc}
}

func (c *surveyServiceClient) SubmitSurvey(ctx context.Context, in *SubmitSurveyRequest, opts ...grpc.CallOption) (*SubmitSurveyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubmitSurveyResponse)
	err := c.cc.Invoke(ctx, SurveyService_SubmitSurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) Signin(ctx context.Context, in *SigninRequest, opts ...grpc.CallOption) (*SigninResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SigninResponse)
	err := c.cc.Invoke(ctx, SurveyService_Signin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) Config(ctx context.Context, in *ConfigRequest, opts ...grpc.CallOption) (*ConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConfigResponse)
	err := c.cc.Invoke(ctx, SurveyService_Config_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) Answer(ctx context.Context, in *AnswerRequest, opts ...grpc.CallOption) (*AnswerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnswerResponse)
	err := c.cc.Invoke(ctx, SurveyService_Answer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GroupConfig(ctx context.Context, in *GroupConfigRequest, opts ...grpc.CallOption) (*GroupConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GroupConfigResponse)
	err := c.cc.Invoke(ctx, SurveyService_GroupConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) Health(ctx context.Context, in *HealthRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_Health_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SurveyServiceServer is the server API for SurveyService service.
// All implementations must embed UnimplementedSurveyServiceServer
// for forward compatibility
//
// 问卷
type SurveyServiceServer interface {
	// 问卷提交
	// 角色或者账号登录需验证authority
	SubmitSurvey(context.Context, *SubmitSurveyRequest) (*SubmitSurveyResponse, error)
	// 登陆
	Signin(context.Context, *SigninRequest) (*SigninResponse, error)
	// 获取配置
	Config(context.Context, *ConfigRequest) (*ConfigResponse, error)
	// 获取问卷答题信息
	// 角色或者账号登录需验证authority
	Answer(context.Context, *AnswerRequest) (*AnswerResponse, error)
	// 获取问卷组配置
	GroupConfig(context.Context, *GroupConfigRequest) (*GroupConfigResponse, error)
	// 健康检测
	Health(context.Context, *HealthRequest) (*xtype.Empty, error)
	mustEmbedUnimplementedSurveyServiceServer()
}

// UnimplementedSurveyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSurveyServiceServer struct {
}

func (UnimplementedSurveyServiceServer) SubmitSurvey(context.Context, *SubmitSurveyRequest) (*SubmitSurveyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitSurvey not implemented")
}
func (UnimplementedSurveyServiceServer) Signin(context.Context, *SigninRequest) (*SigninResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Signin not implemented")
}
func (UnimplementedSurveyServiceServer) Config(context.Context, *ConfigRequest) (*ConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Config not implemented")
}
func (UnimplementedSurveyServiceServer) Answer(context.Context, *AnswerRequest) (*AnswerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Answer not implemented")
}
func (UnimplementedSurveyServiceServer) GroupConfig(context.Context, *GroupConfigRequest) (*GroupConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupConfig not implemented")
}
func (UnimplementedSurveyServiceServer) Health(context.Context, *HealthRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Health not implemented")
}
func (UnimplementedSurveyServiceServer) mustEmbedUnimplementedSurveyServiceServer() {}

// UnsafeSurveyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SurveyServiceServer will
// result in compilation errors.
type UnsafeSurveyServiceServer interface {
	mustEmbedUnimplementedSurveyServiceServer()
}

func RegisterSurveyServiceServer(s grpc.ServiceRegistrar, srv SurveyServiceServer) {
	s.RegisterService(&SurveyService_ServiceDesc, srv)
}

func _SurveyService_SubmitSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitSurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SubmitSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SubmitSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SubmitSurvey(ctx, req.(*SubmitSurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_Signin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SigninRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).Signin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_Signin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).Signin(ctx, req.(*SigninRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_Config_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).Config(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_Config_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).Config(ctx, req.(*ConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_Answer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnswerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).Answer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_Answer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).Answer(ctx, req.(*AnswerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GroupConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GroupConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GroupConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GroupConfig(ctx, req.(*GroupConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_Health_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).Health(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_Health_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).Health(ctx, req.(*HealthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SurveyService_ServiceDesc is the grpc.ServiceDesc for SurveyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SurveyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "papegames.sparrow.survey.SurveyService",
	HandlerType: (*SurveyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitSurvey",
			Handler:    _SurveyService_SubmitSurvey_Handler,
		},
		{
			MethodName: "Signin",
			Handler:    _SurveyService_Signin_Handler,
		},
		{
			MethodName: "Config",
			Handler:    _SurveyService_Config_Handler,
		},
		{
			MethodName: "Answer",
			Handler:    _SurveyService_Answer_Handler,
		},
		{
			MethodName: "GroupConfig",
			Handler:    _SurveyService_GroupConfig_Handler,
		},
		{
			MethodName: "Health",
			Handler:    _SurveyService_Health_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/survey.proto",
}
