// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/cms_survey_group.proto

package proto

func (x *GroupSurvey) Validate() error {
	return nil
}

func (x *GroupSetting) Validate() error {
	return nil
}

func (x *CmsSurveyGroupVersion) Validate() error {
	if v, ok := interface{}(x.GetSettings()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CmsSurveyGroupVersionValidationError{
				field:   "Settings",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

type GroupSurveyValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GroupSurveyValidationError) Field() string { return e.field }

func (e GroupSurveyValidationError) Reason() string { return e.reason }

func (e GroupSurveyValidationError) Message() string { return e.message }

func (e GroupSurveyValidationError) Cause() error { return e.cause }

func (e GroupSurveyValidationError) ErrorName() string { return "GroupSurveyValidationError" }

func (e GroupSurveyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GroupSurvey." + e.field + ": " + e.message + cause
}

type GroupSettingValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GroupSettingValidationError) Field() string { return e.field }

func (e GroupSettingValidationError) Reason() string { return e.reason }

func (e GroupSettingValidationError) Message() string { return e.message }

func (e GroupSettingValidationError) Cause() error { return e.cause }

func (e GroupSettingValidationError) ErrorName() string { return "GroupSettingValidationError" }

func (e GroupSettingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GroupSetting." + e.field + ": " + e.message + cause
}

type CmsSurveyGroupVersionValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyGroupVersionValidationError) Field() string { return e.field }

func (e CmsSurveyGroupVersionValidationError) Reason() string { return e.reason }

func (e CmsSurveyGroupVersionValidationError) Message() string { return e.message }

func (e CmsSurveyGroupVersionValidationError) Cause() error { return e.cause }

func (e CmsSurveyGroupVersionValidationError) ErrorName() string {
	return "CmsSurveyGroupVersionValidationError"
}

func (e CmsSurveyGroupVersionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyGroupVersion." + e.field + ": " + e.message + cause
}
