// Code generated by protoc-gen-http. DO NOT EDIT.
// versions:
// protoc-gen-http v1.4.0
// protoc          v4.25.1
// source: survey
package proto

import (
	context "context"
	gin "github.com/gin-gonic/gin"
	ecode "gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	server "gitlab.papegames.com/fringe/sparrow/pkg/server"
	xgin "gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	xlog "gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	io "io"
)

func RegisterSurveyServiceAdminGinServer(s *xgin.Server, srv SurveyServiceAdminServer) {
	eng := s.GetGinEngine()
	_v1_survey_internal := eng.Group("/v1/survey/internal")
	xgin.RegisterHandler(_v1_survey_internal,
		"POST", "/config/reload",
		_SurveyServiceAdminGin_ConfigReload_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/internal/config/reload", "ConfigReload")
}

func _SurveyServiceAdminGin_ConfigReload_Handler(srv SurveyServiceAdminServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/internal/config/reload",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.ConfigReload(ctx, in.(*ConfigReloadRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(ConfigReloadRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.ConfigReload(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func RegisterSurveyServiceGinServer(s *xgin.Server, srv SurveyServiceServer) {
	eng := s.GetGinEngine()
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/submit",
		_SurveyServiceGin_SubmitSurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/submit", "SubmitSurvey")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/signin",
		_SurveyServiceGin_Signin_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/signin", "Signin")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/config",
		_SurveyServiceGin_Config_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/config", "Config")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/answer",
		_SurveyServiceGin_Answer_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/answer", "Answer")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/group/config",
		_SurveyServiceGin_GroupConfig_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/group/config", "GroupConfig")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/health",
		_SurveyServiceGin_Health_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/health", "Health")
}

func _SurveyServiceGin_SubmitSurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/submit",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SubmitSurvey(ctx, in.(*SubmitSurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SubmitSurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		var err error
		in.Body, err = io.ReadAll(c.Request.Body)
		if err != nil {
			logger.Error("io.ReadAll with error",
				xlog.Err(err))
			return nil, ecode.BadRequest
		}
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SubmitSurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_Signin_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/signin",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Signin(ctx, in.(*SigninRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SigninRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Signin(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_Config_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/config",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Config(ctx, in.(*ConfigRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(ConfigRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Config(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_Answer_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/answer",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Answer(ctx, in.(*AnswerRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(AnswerRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Answer(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GroupConfig_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/group/config",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GroupConfig(ctx, in.(*GroupConfigRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(GroupConfigRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GroupConfig(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_Health_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/health",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Health(ctx, in.(*HealthRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(HealthRequest)
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Health(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}
