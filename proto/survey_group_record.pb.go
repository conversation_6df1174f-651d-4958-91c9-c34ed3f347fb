// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_group_record.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SurveyGroupRecord
type SurveyGroupRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The suvery record id
	Id       int32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" gorm:"primaryKey"`
	Openid   string           `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid" gorm:"column:openid"`
	RoleId   string           `protobuf:"bytes,3,opt,name=role_id,json=roleId,proto3" json:"role_id" gorm:"column:role_id"`
	DeviceId string           `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id" gorm:"column:device_id"`
	Ip       string           `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip" gorm:"column:ip"`
	Ctime    *xtype.Timestamp `protobuf:"bytes,12,opt,name=ctime,proto3" json:"ctime" gorm:"column:ctime"`
}

func (x *SurveyGroupRecord) Reset() {
	*x = SurveyGroupRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupRecord) ProtoMessage() {}

func (x *SurveyGroupRecord) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupRecord.ProtoReflect.Descriptor instead.
func (*SurveyGroupRecord) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_record_proto_rawDescGZIP(), []int{0}
}

func (x *SurveyGroupRecord) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyGroupRecord) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *SurveyGroupRecord) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *SurveyGroupRecord) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SurveyGroupRecord) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SurveyGroupRecord) GetCtime() *xtype.Timestamp {
	if x != nil {
		return x.Ctime
	}
	return nil
}

var File_proto_survey_group_record_proto protoreflect.FileDescriptor

var file_proto_survey_group_record_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x1e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72,
	0x2f, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xab, 0x03,
	0x0a, 0x11, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x24, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x14, 0xd2, 0xa7, 0x86, 0x07, 0x0f, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x02, 0x69, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xba, 0x47, 0x03, 0x78, 0x80,
	0x01, 0xd2, 0xa7, 0x86, 0x07, 0x12, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x3a, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x12, 0x48, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2f, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x13, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x09, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xba,
	0x47, 0x03, 0x78, 0xc8, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x15, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0xd2,
	0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xba, 0x47, 0x03, 0x78, 0xc8, 0x01,
	0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x3a, 0x69, 0x70, 0xd2, 0xa7, 0x86, 0x07, 0x07, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x69, 0x70, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x56, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x25, 0xd2,
	0xa7, 0x86, 0x07, 0x11, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a,
	0x63, 0x74, 0x69, 0x6d, 0x65, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63,
	0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x5d, 0x0a, 0x1c, 0x63,
	0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_proto_survey_group_record_proto_rawDescOnce sync.Once
	file_proto_survey_group_record_proto_rawDescData = file_proto_survey_group_record_proto_rawDesc
)

func file_proto_survey_group_record_proto_rawDescGZIP() []byte {
	file_proto_survey_group_record_proto_rawDescOnce.Do(func() {
		file_proto_survey_group_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_group_record_proto_rawDescData)
	})
	return file_proto_survey_group_record_proto_rawDescData
}

var file_proto_survey_group_record_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_survey_group_record_proto_goTypes = []any{
	(*SurveyGroupRecord)(nil), // 0: papegames.sparrow.survey.SurveyGroupRecord
	(*xtype.Timestamp)(nil),   // 1: papegames.type.Timestamp
}
var file_proto_survey_group_record_proto_depIdxs = []int32{
	1, // 0: papegames.sparrow.survey.SurveyGroupRecord.ctime:type_name -> papegames.type.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_survey_group_record_proto_init() }
func file_proto_survey_group_record_proto_init() {
	if File_proto_survey_group_record_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_group_record_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_group_record_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_group_record_proto_goTypes,
		DependencyIndexes: file_proto_survey_group_record_proto_depIdxs,
		MessageInfos:      file_proto_survey_group_record_proto_msgTypes,
	}.Build()
	File_proto_survey_group_record_proto = out.File
	file_proto_survey_group_record_proto_rawDesc = nil
	file_proto_survey_group_record_proto_goTypes = nil
	file_proto_survey_group_record_proto_depIdxs = nil
}
