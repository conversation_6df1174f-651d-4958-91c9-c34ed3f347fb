// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/client_config.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClientConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientid        string `protobuf:"bytes,1,opt,name=clientid,proto3" json:"clientid,omitempty"`
	Secret          string `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty"`
	DefaultTimezone int32  `protobuf:"varint,3,opt,name=default_timezone,json=defaultTimezone,proto3" json:"default_timezone,omitempty"`
}

func (x *ClientConfig) Reset() {
	*x = ClientConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_client_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConfig) ProtoMessage() {}

func (x *ClientConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_client_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConfig.ProtoReflect.Descriptor instead.
func (*ClientConfig) Descriptor() ([]byte, []int) {
	return file_proto_client_config_proto_rawDescGZIP(), []int{0}
}

func (x *ClientConfig) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *ClientConfig) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *ClientConfig) GetDefaultTimezone() int32 {
	if x != nil {
		return x.DefaultTimezone
	}
	return 0
}

var File_proto_client_config_proto protoreflect.FileDescriptor

var file_proto_client_config_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x22, 0x6d, 0x0a, 0x0c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x7a, 0x6f, 0x6e, 0x65, 0x42, 0x5d, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x50, 0x01, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65,
	0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_client_config_proto_rawDescOnce sync.Once
	file_proto_client_config_proto_rawDescData = file_proto_client_config_proto_rawDesc
)

func file_proto_client_config_proto_rawDescGZIP() []byte {
	file_proto_client_config_proto_rawDescOnce.Do(func() {
		file_proto_client_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_client_config_proto_rawDescData)
	})
	return file_proto_client_config_proto_rawDescData
}

var file_proto_client_config_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_client_config_proto_goTypes = []any{
	(*ClientConfig)(nil), // 0: papegames.sparrow.survey.ClientConfig
}
var file_proto_client_config_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_client_config_proto_init() }
func file_proto_client_config_proto_init() {
	if File_proto_client_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_client_config_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ClientConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_client_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_client_config_proto_goTypes,
		DependencyIndexes: file_proto_client_config_proto_depIdxs,
		MessageInfos:      file_proto_client_config_proto_msgTypes,
	}.Build()
	File_proto_client_config_proto = out.File
	file_proto_client_config_proto_rawDesc = nil
	file_proto_client_config_proto_goTypes = nil
	file_proto_client_config_proto_depIdxs = nil
}
