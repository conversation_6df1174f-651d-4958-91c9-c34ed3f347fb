syntax = "proto3";

package papegames.sparrow.survey;

import "papegames/type/timestamp.proto";

option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message PreAward {
  int64 id = 1;

  string clientid = 2;

  int32  platform = 3;

  int32 zoneid = 4;

  string openid = 5;
  
  string sender = 6;

  string title = 7;

  string body = 8;

  string content = 9;

  papegames.type.Timestamp stime = 10;

  papegames.type.Timestamp etime = 11;

  int32 permanent = 12;

  papegames.type.Timestamp mtime = 13;

  papegames.type.Timestamp ctime = 14;
  
  int32 isgot = 15;
}
