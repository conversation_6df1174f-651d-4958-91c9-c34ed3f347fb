syntax = "proto3";

package papegames.sparrow.survey;

import "papegames/type/timestamp.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto";

option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

// SurveyGroupRecord
message SurveyGroupRecord {
  // The suvery record id
  int32 id = 1 [
    (tagger.tags) = "gorm:primaryKey"
  ];
  string openid = 2 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "gorm:column:openid",
    (tagger.tags) = "json:openid"
  ];

  string role_id = 3 [
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "gorm:column:role_id",
    (tagger.tags) = "json:role_id"
  ];

  string device_id = 4 [
    (openapi.v3.property) = {
      max_length: 200,
    },
    (tagger.tags) = "gorm:column:device_id",
    (tagger.tags) = "json:device_id"
  ];

  string ip = 5 [
    (openapi.v3.property) = {
      max_length: 200,
    },
    (tagger.tags) = "gorm:column:ip",
    (tagger.tags) = "json:ip"
  ];

  papegames.type.Timestamp ctime = 12 [
    (tagger.tags) = "gorm:column:ctime",
    (tagger.tags) = "json:ctime"
  ];
}