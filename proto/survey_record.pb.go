// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_record.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SurveyRecord
type SurveyRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The suvery record id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" gorm:"primaryKey"`
	// The survey record begin time
	BeginTime *xtype.Timestamp `protobuf:"bytes,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	// The survey record end time
	EndTime *xtype.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// The survey record create time
	Ctime    *xtype.Timestamp `protobuf:"bytes,6,opt,name=ctime,proto3" json:"ctime,omitempty"`
	RoleId   string           `protobuf:"bytes,7,opt,name=role_id,json=roleId,proto3" json:"role_id" gorm:"column:role_id"`
	Openid   string           `protobuf:"bytes,8,opt,name=openid,proto3" json:"openid" gorm:"column:openid"`
	DeviceId string           `protobuf:"bytes,9,opt,name=device_id,json=deviceId,proto3" json:"device_id" gorm:"column:device_id"`
	Ip       string           `protobuf:"bytes,10,opt,name=ip,proto3" json:"ip" gorm:"column:ip"`
	IsValid  int32            `protobuf:"varint,11,opt,name=is_valid,json=isValid,proto3" json:"is_valid" gorm:"column:is_valid"`
	Extra    string           `protobuf:"bytes,12,opt,name=extra,proto3" json:"extra" gorm:"column:extra"`
}

func (x *SurveyRecord) Reset() {
	*x = SurveyRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecord) ProtoMessage() {}

func (x *SurveyRecord) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecord.ProtoReflect.Descriptor instead.
func (*SurveyRecord) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{0}
}

func (x *SurveyRecord) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyRecord) GetBeginTime() *xtype.Timestamp {
	if x != nil {
		return x.BeginTime
	}
	return nil
}

func (x *SurveyRecord) GetEndTime() *xtype.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *SurveyRecord) GetCtime() *xtype.Timestamp {
	if x != nil {
		return x.Ctime
	}
	return nil
}

func (x *SurveyRecord) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *SurveyRecord) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *SurveyRecord) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SurveyRecord) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SurveyRecord) GetIsValid() int32 {
	if x != nil {
		return x.IsValid
	}
	return 0
}

func (x *SurveyRecord) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

// SurveyRecordDetail
type SurveyRecordDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The suvery record detail id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" gorm:"primaryKey"`
	// The survey record detail question
	Question string `protobuf:"bytes,4,opt,name=question,proto3" json:"question,omitempty" gorm:"size(64)"`
	// The survey record detail option
	Option string `protobuf:"bytes,5,opt,name=option,proto3" json:"option,omitempty" gorm:"size(64)"`
	// The survey record detail text
	Text           string `protobuf:"bytes,6,opt,name=text,proto3" json:"text,omitempty" gorm:"size(256)"`
	SurveyRecordId int32  `protobuf:"varint,7,opt,name=survey_record_id,json=surveyRecordId,proto3" json:"survey_record_id" gorm:"column:survey_record_id"`
}

func (x *SurveyRecordDetail) Reset() {
	*x = SurveyRecordDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordDetail) ProtoMessage() {}

func (x *SurveyRecordDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordDetail.ProtoReflect.Descriptor instead.
func (*SurveyRecordDetail) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{1}
}

func (x *SurveyRecordDetail) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyRecordDetail) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SurveyRecordDetail) GetOption() string {
	if x != nil {
		return x.Option
	}
	return ""
}

func (x *SurveyRecordDetail) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SurveyRecordDetail) GetSurveyRecordId() int32 {
	if x != nil {
		return x.SurveyRecordId
	}
	return 0
}

var File_proto_survey_record_proto protoreflect.FileDescriptor

var file_proto_survey_record_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x1e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x33,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf4, 0x04, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xd2, 0xa7, 0x86, 0x07, 0x0f, 0x67, 0x6f, 0x72, 0x6d, 0x3a,
	0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38,
	0x0a, 0x0a, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x62,
	0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2f,
	0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x48, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2f, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x13, 0x67, 0x6f, 0x72,
	0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xba, 0x47, 0x03, 0x78, 0x80,
	0x01, 0xd2, 0xa7, 0x86, 0x07, 0x12, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x3a, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x12, 0x50, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x33, 0xba, 0x47, 0x03, 0x78, 0xc8, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x15,
	0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x35, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25,
	0xba, 0x47, 0x03, 0x78, 0xc8, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x67, 0x6f, 0x72, 0x6d, 0x3a,
	0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x69, 0x70, 0xd2, 0xa7, 0x86, 0x07, 0x07, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x69, 0x70, 0x52, 0x02, 0x69, 0x70, 0x12, 0x46, 0x0a, 0x08, 0x69, 0x73, 0x5f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2b, 0xd2, 0xa7, 0x86,
	0x07, 0x14, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x69, 0x73,
	0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x69, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x07, 0x69, 0x73, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x12, 0x3b, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x25, 0xd2, 0xa7, 0x86, 0x07, 0x11, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x3a, 0x65, 0x78, 0x74, 0x72, 0x61, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0xc2,
	0x02, 0x0a, 0x12, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x24, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x14, 0xd2, 0xa7, 0x86, 0x07, 0x0f, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x08, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xba,
	0x47, 0x05, 0x78, 0x40, 0x80, 0x01, 0x06, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x67, 0x6f, 0x72, 0x6d,
	0x3a, 0x73, 0x69, 0x7a, 0x65, 0x28, 0x36, 0x34, 0x29, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1a, 0xba, 0x47, 0x05, 0x78, 0x40, 0x80, 0x01, 0x06, 0xd2, 0xa7, 0x86,
	0x07, 0x0d, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x73, 0x69, 0x7a, 0x65, 0x28, 0x36, 0x34, 0x29, 0x52,
	0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0xba, 0x47, 0x03, 0x78, 0x80, 0x02, 0xd2, 0xa7, 0x86,
	0x07, 0x0e, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x73, 0x69, 0x7a, 0x65, 0x28, 0x32, 0x35, 0x36, 0x29,
	0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x6b, 0x0a, 0x10, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x41, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x1c, 0x67, 0x6f, 0x72,
	0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x15, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x52, 0x0e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x64, 0x42, 0x5d, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x50, 0x01, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_record_proto_rawDescOnce sync.Once
	file_proto_survey_record_proto_rawDescData = file_proto_survey_record_proto_rawDesc
)

func file_proto_survey_record_proto_rawDescGZIP() []byte {
	file_proto_survey_record_proto_rawDescOnce.Do(func() {
		file_proto_survey_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_record_proto_rawDescData)
	})
	return file_proto_survey_record_proto_rawDescData
}

var file_proto_survey_record_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_survey_record_proto_goTypes = []any{
	(*SurveyRecord)(nil),       // 0: papegames.sparrow.survey.SurveyRecord
	(*SurveyRecordDetail)(nil), // 1: papegames.sparrow.survey.SurveyRecordDetail
	(*xtype.Timestamp)(nil),    // 2: papegames.type.Timestamp
}
var file_proto_survey_record_proto_depIdxs = []int32{
	2, // 0: papegames.sparrow.survey.SurveyRecord.begin_time:type_name -> papegames.type.Timestamp
	2, // 1: papegames.sparrow.survey.SurveyRecord.end_time:type_name -> papegames.type.Timestamp
	2, // 2: papegames.sparrow.survey.SurveyRecord.ctime:type_name -> papegames.type.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proto_survey_record_proto_init() }
func file_proto_survey_record_proto_init() {
	if File_proto_survey_record_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_record_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_record_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_record_proto_goTypes,
		DependencyIndexes: file_proto_survey_record_proto_depIdxs,
		MessageInfos:      file_proto_survey_record_proto_msgTypes,
	}.Build()
	File_proto_survey_record_proto = out.File
	file_proto_survey_record_proto_rawDesc = nil
	file_proto_survey_record_proto_goTypes = nil
	file_proto_survey_record_proto_depIdxs = nil
}
