// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/pre_award_template.proto

package proto

func (x *PreAwardTemplate) Validate() error {
	return nil
}

type PreAwardTemplateValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PreAwardTemplateValidationError) Field() string { return e.field }

func (e PreAwardTemplateValidationError) Reason() string { return e.reason }

func (e PreAwardTemplateValidationError) Message() string { return e.message }

func (e PreAwardTemplateValidationError) Cause() error { return e.cause }

func (e PreAwardTemplateValidationError) ErrorName() string { return "PreAwardTemplateValidationError" }

func (e PreAwardTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PreAwardTemplate." + e.field + ": " + e.message + cause
}
