syntax = "proto3";

package papegames.sparrow.survey;

import "papegames/type/timestamp.proto";
import "papegames/type/raw_message.proto";
import "tagger/tagger.proto";

option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";


message CmsSurveyVersion {
  message Setting {
    BaseRuleConfig baseRuleConfig = 1;

    AnswerLimitConfig answerLimitConfig = 2;

    GiftConfig giftConfig = 3;

    papegames.type.RawMessage materialsConfig  = 4;

    // 区服校验
    repeated int32 zoneIds = 14;
  }

  message WebSettings {
    repeated string languageList = 1;
  }

  message AnswerLimitConfig {
    // 限制方式 0 设备限制  1 IP限制
    string limitType = 1;
  }

  message BaseRuleConfig {
    // 登陆方式  0 : 角色登陆 1: 账号登陆 2: 不登录
    string loginType = 1;

    AnswerTimesConfig answerTimesConfig = 2;
    // 频率控制
    PeriodicControl periodicControl = 3;
  }

  message AnswerTimesConfig {
    // 1: 仅一次 2: 限定次数 3: 无限制
    int32 limitType = 1;

    // 次数
    int32 times = 2;
  }

  // 频率空配置
  message PeriodicControl {
    // 开关，默认关闭
    bool enable = 1;
    // 循环值
    int32 interval = 2;
    // unit
    enum Unit {
      Unit_Unknown = 0;
      Unit_Minute = 1;
      Unit_Hour = 2;
      Unit_Day = 3;
    }
    // 时间单位
    Unit unit = 3;
  }

  message GiftConfig {
    // 是否由CMS发奖
    bool isGiveOutByCms = 1;

    // 0: 邮件 1:兑换码 2:推送平台
    string giveOutType = 2;

    PreAwardConfig preAwardConfig = 3;

    RedeemConfig redeemConfig = 4;

    // 0: 账号维度 1:角色维度
    string giveOutMethod = 5;

    // 推送平台邮
    PushAwardConfig pushAwardConfig = 6;
  }

  message PreAwardConfig {
    string id = 1;
  }

  message RedeemConfig {
    // 兑换码头
    string redeemHead = 1;
  }

  message PushAwardConfig {
    // 场景code
    string sceneCode = 1;

    // 渠道code
    string channelCode = 2;

    // 策略id
    int64 strategyId = 3;
  }

  int64 id = 1 [
    (tagger.tags) = "gorm:primaryKey"
  ];

  string clientid = 2;

  string name = 3;

  int32 is_closed = 4;

  int32 is_pause = 5;

  papegames.type.Timestamp stime = 6;

  papegames.type.Timestamp etime = 7;

  string settings = 8;

  Setting settingStruct = 9 [
    (tagger.tags) = "gorm:-"
  ];

  string hash_code = 10;

  int32 is_delete = 11;

  string web_settings = 13;

  string default_language = 14 [
    (tagger.tags) = "gorm:-"
  ];

  bytes schema = 15;

  bytes schema_dec = 16 [
    (tagger.tags) = "gorm:-"
  ];

  bytes multilingual_schema = 17;

  map<string,bytes> multilingual_schema_dec_map = 18 [
    (tagger.tags) = "gorm:-"
  ];
  int32 survey_id = 19;

  bytes font = 20;

  string key_value = 21 [(tagger.tags) = "gorm:type:text;comment:中文/英文文案", (tagger.tags) = "json:key_value"];

  map<string,string> key_value_map = 22 [
    (tagger.tags) = "gorm:-"
  ];
}
