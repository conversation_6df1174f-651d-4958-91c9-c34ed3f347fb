// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/client_config.proto

package proto

func (x *ClientConfig) Validate() error {
	return nil
}

type ClientConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ClientConfigValidationError) Field() string { return e.field }

func (e ClientConfigValidationError) Reason() string { return e.reason }

func (e ClientConfigValidationError) Message() string { return e.message }

func (e ClientConfigValidationError) Cause() error { return e.cause }

func (e ClientConfigValidationError) ErrorName() string { return "ClientConfigValidationError" }

func (e ClientConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ClientConfig." + e.field + ": " + e.message + cause
}
