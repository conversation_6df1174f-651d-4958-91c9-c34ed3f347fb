syntax = "proto3";

package papegames.sparrow.survey;

import "papegames/type/timestamp.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto";

option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

// SurveyStrategyRecord
message SurveyStrategyRecord {
    // The suvery strategy record id
    string clientid = 1 [
      (tagger.tags) = "gorm:primaryKey"
    ]; 
    int64 strategy_id = 2 [
      (tagger.tags) = "gorm:primaryKey;column:strategy_id",
      (tagger.tags) = "json:strategy_id"
    ];
    string openid = 3 [
      (openapi.v3.property) = {
        max_length: 64,
      },
      (tagger.tags) = "gorm:primaryKey;column:openid",
      (tagger.tags) = "json:openid"
    ];
    string role_id = 4 [
      (openapi.v3.property) = {
        max_length: 64,
      },
      (tagger.tags) = "gorm:primaryKey;column:role_id",
      (tagger.tags) = "json:role_id"
    ];
    string survey_id = 5 [
      (openapi.v3.property) = {
        max_length: 64,
      },
      (tagger.tags) = "gorm:column:survey_id",
      (tagger.tags) = "json:survey_id"
    ];
    // The survey record create time
    papegames.type.Timestamp ctime = 6;
  }