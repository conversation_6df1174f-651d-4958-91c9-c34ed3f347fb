syntax = "proto3";

package papegames.sparrow.survey;

import "tagger/tagger.proto";

option go_package = "gitlab.papegames.com/fringe/survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message RoleidToOpenid {
  int64 id = 1 [
    (tagger.tags) = "gorm:primaryKey"
  ];
  string clientid = 2;
  int32 platform = 3;
  int32 zoneid = 4;
  string openid = 5;
  string roleid = 6;
}
