package server

import (
	"context"

	"github.com/gin-gonic/gin"
	"gitlab.papegames.com/fringe/sparrow/pkg"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/local"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/log"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/origin"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/trace"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/ulimit"
	"gitlab.papegames.com/fringe/sparrow/pkg/server"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtrace"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/control"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/proto"
	chooks "gitlab.papegames.com/fringe/survey/server/hooks"
)

var ginServer = xgin.NewServer()

var adminGinServer = xgin.NewServer()

func Get() *xgin.Server { return ginServer }
func GetAdmin() *xgin.Server {
	return adminGinServer
}

func Startup() error {
	err := xtrace.StdConfig().Build()
	if err != nil {
		return err
	}

	ginServer.Init(
		server.ServiceName(pkg.AppName),
		server.ServiceHost(config.Get().Host),
		server.ServiceRegistrar(config.Get().Register),
	)
	adminGinServer.Init(
		server.ServiceName(pkg.AppName),
		server.ServiceHost(config.Get().AdminHost),
		server.ServiceRegistrar(config.Get().Register),
	)

	hooks.Append(origin.New())
	hooks.Append(log.New())
	hooks.Append(chooks.NewRecover())
	hooks.Append(chooks.NewFeishuWebhook())
	hooks.Append(ulimit.New(func() *ulimit.Config {
		conf := ulimit.StdConfig()
		// conf.GetUser = func(c *gin.Context) (string, bool) {
		// 	ip := xnet.SourceIP(c.Request, config.Get().IpTrust)
		// 	return ip, ip != ""
		// }

		conf.GetUser = func(ctx context.Context) (string, bool) {
			c := xgin.FromContext(ctx)
			ip := xnet.SourceIP(c.Request, config.Get().IpTrust)
			return ip, ip != ""
		}
		return conf
	}))
	hooks.Append(trace.New())
	hooks.Append(local.New(), "internal")
	ginServer.GetGinEngine().Use(hooks.GetHandlerFunc()...)

	adminGinServer.GetGinEngine().Use(hooks.GetHandlerFunc()...)

	eng := ginServer.GetGinEngine()
	xgin.RegisterInterceptor(eng, registerContext)
	proto.RegisterSurveyServiceGinServer(ginServer, control.Get())

	engAdmin := adminGinServer.GetGinEngine()
	xgin.RegisterInterceptor(engAdmin, registerContext)
	proto.RegisterSurveyServiceAdminGinServer(adminGinServer, control.GetAdmin())

	return nil
}

func registerContext(next xgin.Handler) xgin.Handler {
	return func(c *gin.Context) (interface{}, ecode.Code) {
		ctx := xgin.Context(c)
		d := database.Get()
		d = xgorm.WithContext(d, ctx)
		ctx = xgorm.NewContext(ctx, d)
		xgin.WithContext(c, ctx)
		return next(c)
	}
}
