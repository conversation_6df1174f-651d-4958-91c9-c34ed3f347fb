package hooks

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync/atomic"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"google.golang.org/grpc"

	"gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	"gitlab.papegames.com/fringe/sparrow/pkg/safe"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xhttp"
)

const (
	defaultKey = "feishu_webhook"
)

var (
	client = xhttp.NewClient()
)

type Config struct {
	Enable     bool   `xconf:"enable"`
	Webhook    string `xconf:"webhook"`
	Prefix     string `xconf:"prefix"`
	Ignore     string `xconf:"ignore"`
	IgnoreList []string
}

type logger struct {
	config atomic.Pointer[Config]
}

func NewFeishuWebhook() hooks.Hook {
	h := new(logger)
	err := h.Reload()
	if err != nil {
		xlog.Panic("hooks.Log: New() with error",
			xlog.Err(err))
	}
	xconf.RegisterReload(h.Reload)
	return h
}
func (h *logger) HandleHTTP(c *gin.Context) {
	if h.config.Load().Enable {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		logger = logger.WithOptions(h.zapHook())
		ctx = xlog.NewContext(ctx, logger)
		xgin.WithContext(c, ctx)
	}
	c.Next()
}
func (h *logger) HandleGRPC(ctx context.Context, request any,
	info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (response any, err error) {
	logger := xlog.FromContext(ctx)
	logger = logger.WithOptions(h.zapHook())
	ctx = xlog.NewContext(ctx, logger)
	return handler(ctx, request)
}
func (h *logger) zapHook() zap.Option {
	return zap.Hooks(func(entry zapcore.Entry) error {
		cfg := h.config.Load()
		if entry.Level >= zap.ErrorLevel && cfg.Webhook != "" {
			for _, one := range cfg.IgnoreList {
				if one != "" && strings.Contains(entry.Message, one) {
					return nil
				}
			}
			safe.Go(func() {
				msg := fmt.Sprintf("%s\ntime: [%s]\nCaller: [%s]\n\nmessage: [%s]",
					cfg.Prefix,
					entry.Time.String(),
					entry.Caller.String(),
					entry.Message,
				)
				data := map[string]interface{}{
					"msg_type": "text",
					"content": map[string]interface{}{
						"text": msg,
					},
				}
				buf, err := json.Marshal(data)
				if err != nil {
					return
				}
				request, err := http.NewRequest(http.MethodPost, cfg.Webhook, bytes.NewReader(buf))
				if err != nil {
					return
				}
				request.Header.Set("Content-Type", "application/json")
				response, err := client.Do(request)
				if err != nil {
					return
				}
				defer response.Body.Close()
			})
		}
		return nil
	})
}

func (h *logger) Reload() error {
	cfg := DefaultConfig()
	if err := xconf.UnmarshalKey(defaultKey, cfg); err != nil {
		panic(fmt.Sprintf("xgorm.RawConfig with error [%s] [%s]", defaultKey, err.Error()))
	}
	cfg.IgnoreList = strings.Split(cfg.Ignore, "##")
	h.config.Store(cfg)
	return nil
}

func DefaultConfig() *Config {
	return &Config{
		Enable: true,
	}
}
