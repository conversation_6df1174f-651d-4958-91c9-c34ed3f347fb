package hooks

import (
	"time"

	"github.com/gin-gonic/gin"

	"gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	"gitlab.papegames.com/fringe/sparrow/pkg/metric"
	"gitlab.papegames.com/fringe/sparrow/pkg/safe"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
)

var (
	ip, _ = xnet.InternalIP()

	costTime = metric.NewSummaryVec(&metric.SummaryVecOpts{
		Namespace:   "tds",
		Subsystem:   "",
		Name:        "cost_time_seconds",
		Help:        "chaos const time.",
		Objectives:  map[float64]float64{0.5: 0.05, 0.7: 0.03, 0.8: 0.02, 0.9: 0.01, 0.99: 0.001},
		ConstLabels: metric.TargetLabels,
		Labels:      []string{"ip", "uri"},
	})
)

type recovery struct{}

func NewRecover() hooks.Hook {
	r := &recovery{}
	return r
}

func (r *recovery) Handle(c *gin.Context) {
	start := time.Now()

	defer func() {
		safe.Recover(func(err interface{}) {
			ret := new(xgin.Return)
			ret.RequestId = xnet.GetRequestId(c.Request)
			ret.Code = 999
			ret.Info = "panic系统错误。"
			xgin.RenderJSON(c, ret)
			c.Abort()
		})
		costTime.Observe(float64(time.Since(start)), ip, c.Request.URL.Path)
	}()

	c.Next()
}

func (r *recovery) Reload() error {
	return nil
}
