package model

import (
	"context"
	"fmt"
	"time"

	"gitlab.papegames.com/fringe/survey/proto"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

const (
	batchSize = 100 // 批量插入survey_record_detail表的最大数量
)

type SurveyInfo struct {
	Id            int32                           `json:"id"`
	SurveyRecords map[string][]SurveyRecordDetail `json:"survey_records" gorm:"-"`
	BeginTime     time.Time                       `json:"begin_time"`
	EndTime       time.Time                       `json:"end_time"`
	SurveySelector
	// Extra
	Extra string `json:"extra"`
	Falg1 int    `json:"falg1"`
}

type SurveyRecordDetail struct {
	Option string `json:"option"`
	Text   string `json:"text"`
}

type SurveySelector struct {
	// 问卷ID
	SurveyId string `json:"survey_id,omitempty" gorm:"-"`
	// 问卷类型
	SurveyType int `json:"survey_type,omitempty" gorm:"-"`
	// 问卷答题次数
	SurveyAnswerTimes int32 `json:"survey_answer_times" gorm:"-"`
	// 账号ID
	Openid string `json:"openid,omitempty"`
	// 角色ID
	RoleId string `json:"role_id,omitempty"`
	// 设备ID
	DeviceId string `json:"device_id,omitempty"`
	// IP
	Ip string `json:"ip,omitempty"`
}

// 答题次数是否超过限制
func SurveyRecordOverflowed(d *xgorm.DB, ss *SurveySelector) (bool, error) {
	// 无限制会返回-1,返回不受限制
	if ss.SurveyAnswerTimes < 0 {
		return false, nil
	}
	var count int64
	var err error
	tx := d.Table(SurveyRecordTable(ss.SurveyId))
	// 根据不同的答题类型，查询答题记录, 无效的也算
	switch ss.SurveyType {
	case SurveyTypeOpenid:
		err = tx.Where("openid = ? and is_delete = 0", ss.Openid).Count(&count).Error
	case SurveyTypeRoleId:
		err = tx.Where("role_id = ? and is_delete = 0", ss.RoleId).Count(&count).Error
	case SurveyTypeDeviceId:
		err = tx.Where("device_id = ? and is_delete = 0", ss.DeviceId).Count(&count).Error
	case SurveyTypeIp:
		err = tx.Where("ip = ? and is_delete = 0 ", ss.Ip).Count(&count).Error
	case SurveyTypeAny:
		return false, nil
	default:
		err = fmt.Errorf("unknown survey type (%v,%v)", ss.SurveyType, ss.SurveyId)
	}
	if err != nil {
		return false, err
	}
	return count >= int64(ss.SurveyAnswerTimes), nil
}

// 创建答题记录,并获取主键ID
func CreateSurveyRecord(d *xgorm.DB,
	surveyId string,
	surveyRecord *proto.SurveyRecord) (int32, error) {
	surveyRecord.Ctime = xtype.NewTimestamp(time.Now())
	err := d.Table(SurveyRecordTable(surveyId)).Create(surveyRecord).Error
	if err != nil {
		return 0, err
	}
	return surveyRecord.Id, nil
}

// 插入答题记录详情
func InsertSurveyRecordDetails(d *xgorm.DB,
	surveyId string,
	surveyRecordDetails []*proto.SurveyRecordDetail) error {
	return d.Table(SurveyRecordDetailTable(surveyId)).
		CreateInBatches(surveyRecordDetails, batchSize).Error
}

func GetSurveyInfo(d *xgorm.DB, ss *SurveySelector) (*SurveyInfo, error) {
	var surveyInfo SurveyInfo
	var surveyRecordDetails []*proto.SurveyRecordDetail
	var err error

	tx := d.Table(SurveyRecordTable(ss.SurveyId))
	dtx := d.Table(SurveyRecordDetailTable(ss.SurveyId))

	// is_valid = 0 是有效数据,无效的也返回
	switch ss.SurveyType {
	case SurveyTypeOpenid:
		err = tx.Where("openid = ? and is_delete = 0", ss.Openid).Order("ctime desc").First(&surveyInfo).Error
	case SurveyTypeRoleId:
		err = tx.Where("role_id = ? and is_delete = 0", ss.RoleId).Order("ctime desc").First(&surveyInfo).Error
	case SurveyTypeDeviceId:
		err = tx.Where("device_id = ? and is_delete = 0", ss.DeviceId).Order("ctime desc").First(&surveyInfo).Error
	case SurveyTypeIp:
		err = tx.Where("ip = ? and is_delete = 0", ss.Ip).Order("ctime desc").First(&surveyInfo).Error
	case SurveyTypeAny:
		err = tx.Where("is_delete = 0").Order("ctime desc").First(&surveyInfo).Error
	default:
		err = fmt.Errorf("unknown survey type (%v,%v)", ss.SurveyType, ss.SurveyId)
	}
	if err != nil {
		return nil, err
	}

	err = dtx.Where("survey_record_id = ?", surveyInfo.Id).Find(&surveyRecordDetails).Error
	if err != nil {
		return nil, err
	}

	surveyInfo.SurveyId = ss.SurveyId
	surveyInfo.SurveyRecords = make(map[string][]SurveyRecordDetail)

	for _, v := range surveyRecordDetails {
		surveyInfo.SurveyRecords[v.Question] = append(surveyInfo.SurveyRecords[v.Question], SurveyRecordDetail{
			Option: v.Option,
			Text:   v.Text,
		})
	}
	return &surveyInfo, nil
}

func SurveyRecordTable(surveyId string) string {
	return "survey_record_" + surveyId
}

func SurveyRecordDetailTable(surveyId string) string {
	return "survey_record_detail_" + surveyId
}

// 答题周期是否超过限制
func SurveyRecordDurationOverflowed(ctx context.Context, ss *SurveySelector, startTime, endTime string, max int32) (bool, error) {
	var count int64
	var err error
	d := xgorm.FromContext(ctx)
	tx := d.Table(SurveyRecordTable(ss.SurveyId))

	//搜索指定时间范围
	tx.Where("ctime BETWEEN ? AND ?", startTime, endTime)

	// 根据不同的答题类型，查询答题记录, 无效的也算
	switch ss.SurveyType {
	case SurveyTypeOpenid:
		err = tx.Where("openid = ? and is_delete = 0", ss.Openid).Count(&count).Error
	case SurveyTypeRoleId:
		err = tx.Where("role_id = ? and is_delete = 0", ss.RoleId).Count(&count).Error
	case SurveyTypeDeviceId:
		err = tx.Where("device_id = ? and is_delete = 0", ss.DeviceId).Count(&count).Error
	case SurveyTypeIp:
		err = tx.Where("ip = ? and is_delete = 0 ", ss.Ip).Count(&count).Error
	case SurveyTypeAny:
		return false, nil
	default:
		err = fmt.Errorf("unknown survey type (%v,%v)", ss.SurveyType, ss.SurveyId)
	}
	if err != nil {
		return false, err
	}
	return count >= int64(max), nil
}
