package model

import (
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdebug"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
)

func TestMain(m *testing.M) {
	xconf.ReadInConfig()
	err := config.Startup()
	if err != nil {
		panic(err)
	}
	err = database.Startup()
	if err != nil {
		panic(err)
	}
	err = xlog.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	err = xdebug.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	m.Run()
}

func TestGetSurveyGroupRaw(t *testing.T) {
	Convey("TestGetSurveyGroupRaw", t, func() {
		r, err := getSurveyGroupRaw("gs_0x0vn2coe36")
		So(err, ShouldBeNil)
		t.Log(r)
	})
}

func TestGetCmsSurveyGroup(t *testing.T) {
	Convey("GetCmsSurveyGroup", t, func() {
		r, err := GetCmsSurveyGroup("gs_0x0vn2coe36")
		So(err, ShouldBeNil)
		t.Log(r)
	})
}

func TestReloadCmsSurveyGroup(t *testing.T) {
	Convey("ReloadCmsSurveyGroup", t, func() {
		r, err := ReloadCmsSurveyGroup("gs_x1c4v8r8fx9")
		So(err, ShouldBeNil)
		t.Log(r)

		r, err = ReloadCmsSurveyGroup("gs_0x0vn2coe361")
		So(xgorm.RecordNotFound(err), ShouldBeTrue)
		t.Log(r)
	})
}

// func TestCmsSurveyGroupInEffect(t *testing.T) {
// 	Convey("TestCmsSurveyGroupInEffect", t, func() {
// 		r, err := GetCmsSurveyGroup("gs_0x0vn2coe36")
// 		So(err, ShouldBeNil)
// 		t.Log(r)
// 		e := CmsSurveyGroupInEffect(context.Background(), r)
// 		So(e, ShouldBeNil)
// 	})
// }
