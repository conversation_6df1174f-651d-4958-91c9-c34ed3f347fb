# service
host: ":9080"
sparrow:
  log:
    file: stdout
    level: "debug"
  database:

    redis:
      addr: "127.0.0.1:6379"
      pool_size: 5
      max_retries: 2
    
    mysql:
      dataSource: "tds_admin_dev:2p7oMXbaEzgpsbbg@(pc-bp1w81j8101z3d8lx.mysql.polardb.rds.aliyuncs.com:3306)/survey?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 5
      maxOpenConns: 5
    
    naccessories:
      dataSource: "sdk_cms:UYjZrOT0WT@(rm-bp1su8h70fo8d5pz9.mysql.rds.aliyuncs.com:3306)/naccessories?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 5
      maxOpenConns: 5

    naward:
      dataSource: "sdk_cms:UYjZrOT0WT@(rm-bp1su8h70fo8d5pz9.mysql.rds.aliyuncs.com:3306)/naward?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 5
      maxOpenConns: 5
  