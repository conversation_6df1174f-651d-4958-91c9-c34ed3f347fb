package model

import (
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/proto"
)

func TestSurveyGroupRecord(t *testing.T) {
	Convey("Test SurveyGroupRecord", t, func() {

		var (
			openId      = "3"
			roleId      = "3"
			surveyGroup = "gs_0x0vn2coe36"
		)
		id, err := CreateSurveyGroupRecord(database.Get(), surveyGroup, &proto.SurveyGroupRecord{
			Openid:   openId,
			RoleId:   roleId,
			DeviceId: "",
			Ip:       "127.0.0.1",
			Ctime:    xtype.NewTimestamp(time.Now()),
		})

		defer func() {
			So(database.Get().Table(SurveyGroupRecordTable(surveyGroup)).
				Where("openid", openId).Delete(nil).Error, ShouldBeNil)
		}()
		So(id, ShouldBeGreater<PERSON>han, 0)
		So(err, ShouldBeNil)

	})
}

func TestSurveyGroupRecordOverflowed(t *testing.T) {
	Convey("Test SurveyGroupRecordOverflowed", t, func() {

		var (
			openId      = "3"
			roleId      = "3"
			surveyGroup = "gs_0x0vn2coe36"
		)
		id, err := CreateSurveyGroupRecord(database.Get(), surveyGroup, &proto.SurveyGroupRecord{
			Openid:   openId,
			RoleId:   roleId,
			DeviceId: "",
			Ip:       "127.0.0.1",
			Ctime:    xtype.NewTimestamp(time.Now()),
		})
		So(err, ShouldBeNil)
		So(id, ShouldBeGreaterThan, 0)
		defer func() {
			So(database.Get().Table(SurveyGroupRecordTable(surveyGroup)).
				Where("openid", openId).Delete(nil).Error, ShouldBeNil)
		}()

		r, err := SurveyGroupRecordOverflowed(database.Get(), &proto.CmsSurveyGroupVersion{
			HashCode:  surveyGroup,
			LimitType: proto.CmsSurveyGroupVersion_RoleIdOnce,
		}, &SurveySelector{Openid: openId, RoleId: roleId})
		So(r, ShouldBeTrue)
		So(err, ShouldBeNil)

		r, err = SurveyGroupRecordOverflowed(database.Get(), &proto.CmsSurveyGroupVersion{
			HashCode:  surveyGroup,
			LimitType: proto.CmsSurveyGroupVersion_OpenIdOnce,
		}, &SurveySelector{Openid: openId, RoleId: roleId})
		So(r, ShouldBeTrue)
		So(err, ShouldBeNil)

		r, err = SurveyGroupRecordOverflowed(database.Get(), &proto.CmsSurveyGroupVersion{
			HashCode:  surveyGroup,
			LimitType: proto.CmsSurveyGroupVersion_Unlimited,
		}, &SurveySelector{Openid: openId, RoleId: roleId})
		So(r, ShouldBeFalse)
		So(err, ShouldBeNil)
	})
}
