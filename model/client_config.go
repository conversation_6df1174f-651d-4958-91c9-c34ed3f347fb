package model

import (
	"errors"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/cache/memory"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/singleflight"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/proto"
)

var (
	clientConfigs     = memory.NewTTLCache()
	clientConfigGroup = singleflight.Group{}
)

func GetClientConfig(clientid string) (*proto.ClientConfig, error) {
	clientConfig := clientConfigs.Get(clientid)
	if clientConfig != nil {
		cc, ok := clientConfig.(*proto.ClientConfig)
		if ok {
			return cc, nil
		}
	}
	value, _ := clientConfigGroup.Do(clientid, func() (interface{}, error) {
		cs, err := getClientConfigRaw(clientid)
		if err != nil {
			return nil, err
		}
		ttl := config.Get().ClientConfigTTL
		if ttl == 0 {
			ttl = 6 * time.Hour
		}
		clientConfigs.Set(clientid, cs, ttl, nil)
		return cs, nil
	})
	if value.Err != nil {
		return nil, value.Err
	}

	cc, ok := value.Val.(*proto.ClientConfig)
	if !ok {
		return nil, errors.New("client config type error")
	}
	return cc, nil
}

func getClientConfigRaw(clientid string) (*proto.ClientConfig, error) {
	db := database.GetNacc()
	var cc = new(proto.ClientConfig)
	err := db.Where("clientid = ?", clientid).First(cc).Error
	return cc, err
}
