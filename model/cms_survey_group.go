package model

import (
	"errors"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/cache/memory"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/singleflight"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/proto"
)

var (
	surveyGroups   = memory.NewTTLCache()
	surveyGroupsSG = singleflight.Group{}
)

func GetCmsSurveyGroup(groupHashCode string) (*proto.CmsSurveyGroupVersion, error) {
	cmsSurvey := cmsSurveys.Get(groupHashCode)
	if cmsSurvey != nil {
		cs, ok := cmsSurvey.(*proto.CmsSurveyGroupVersion)
		if ok {
			return cs, nil
		}
	}
	return ReloadCmsSurveyGroup(groupHashCode)
}

func getSurveyGroupRaw(groupHashCode string) (*proto.CmsSurveyGroupVersion, error) {
	db := database.Get()
	var group *proto.CmsSurveyGroupVersion
	err := db.Where("hash_code = ?", groupHashCode).Order("id desc").First(&group).Error
	if err != nil {
		return nil, err
	}
	return group, nil
}

func ReloadCmsSurveyGroup(groupHashCode string) (*proto.CmsSurveyGroupVersion, error) {
	value, _ := surveyGroupsSG.Do(groupHashCode, func() (interface{}, error) {
		group, err := getSurveyGroupRaw(groupHashCode)
		if err != nil {
			return nil, err
		}
		ttl := config.Get().CmsSurveyTTL
		if ttl == 0 {
			ttl = 5 * time.Minute
		}
		surveyGroups.Set(groupHashCode, group, ttl, nil)
		return group, nil
	})
	if value.Err != nil {
		return nil, value.Err
	}
	group, ok := value.Val.(*proto.CmsSurveyGroupVersion)
	if !ok {
		return nil, errors.New("cms survey group error")
	}
	return group, nil
}
