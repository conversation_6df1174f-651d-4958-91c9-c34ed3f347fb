package model

import (
	"strconv"
	"strings"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gitlab.papegames.com/fringe/survey/proto"
)

var preAwardV2Clientid = "1008,1019,1035,1037,1030,1033"

func PreAwardTable(clientid string) string {
	cltid, _ := strconv.Atoi(clientid)
	if strings.Contains(preAwardV2Clientid, clientid) || cltid > 1050 {
		return "pre_award_v2"
	}
	return "pre_award"
}

func CreatePreAward(d *xgorm.DB, pat *proto.PreAwardTemplate, openid string, platform int32, zoneid int32) error {
	pa := proto.PreAward{}
	pa.Clientid = pat.GetClientid()
	pa.Platform = platform
	pa.Zoneid = zoneid
	pa.Openid = openid
	pa.Sender = pat.GetSender()
	pa.Title = pat.GetTitle()
	pa.Body = pat.GetBody()
	pa.Content = pat.GetContent()
	pa.Stime = pat.GetStime()
	pa.Etime = pat.GetEtime()
	pa.Permanent = pat.GetPermanent()

	pa.Mtime = xtype.NewTimestamp(time.Now())
	pa.Ctime = xtype.NewTimestamp(time.Now())
	return d.Table(PreAwardTable(pa.Clientid)).Create(&pa).Error
}
