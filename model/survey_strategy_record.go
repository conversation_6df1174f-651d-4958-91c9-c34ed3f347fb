package model

import (
	"fmt"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gitlab.papegames.com/fringe/survey/proto"
	"gorm.io/gorm/clause"
)

func CreateSurveyStrategyRecord(d *xgorm.DB,
	strategyId int64, clientId, openid, roleid, surveyId string) (int64, error) {
	record := new(proto.SurveyStrategyRecord)
	record.Clientid = clientId
	record.StrategyId = strategyId
	record.Openid = openid
	record.RoleId = roleid
	record.SurveyId = surveyId
	record.Ctime = xtype.NewTimestamp(time.Now())
	d = d.Table(SurveyStrategyRecordTable(strategyId)).
		Clauses(clause.OnConflict{DoNothing: true}).Create(record)
	if d.Error != nil {
		return 0, d.Error
	}
	return d.RowsAffected, nil
}

func SurveyStrategyRecordTable(strategyId int64) string {
	return "survey_strategy_record_" + fmt.Sprintf("%02d", strategyId%10)
}
