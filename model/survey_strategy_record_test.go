package model

import (
	"testing"

	_ "github.com/go-sql-driver/mysql"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/survey/database"
)

func TestCreateSurveyStrategyRecord(t *testing.T) {
	Convey("test create SurveyStrategyRecord", t, func() {
		var (
			strategy = int64(3)
			clientId = "2008"
			openId   = "3"
			roleId   = "3"
			surveyId = "xxaaaaa699"
		)
		r, err := CreateSurveyStrategyRecord(database.Get(), strategy, clientId, openId, roleId, surveyId)
		So(err, ShouldBeNil)
		So(r, ShouldEqual, 1)
		r, err = CreateSurveyStrategyRecord(database.Get(), strategy, clientId, openId, roleId, surveyId)
		So(err, ShouldBeNil)
		So(r, ShouldEqual, 0)
		So(database.Get().Table(SurveyStrategyRecordTable(strategy)).
			Where("openid", openId).
			Where("strategy_id", strategy).
			Where("role_id", roleId).
			Where("clientid", clientId).Delete(nil).Error, ShouldBeNil)
	})
}
