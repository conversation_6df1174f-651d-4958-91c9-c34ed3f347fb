package model

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"gitlab.papegames.com/fringe/sparrow/pkg/cache/memory"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/singleflight"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/proto"
	"gitlab.papegames.com/fringe/survey/shared"
)

const (
	LoginTypeRoleid   = "0"
	LoginTypeOpenid   = "1"
	LoginTypeNo       = "2"
	LimitTypeDevice   = "0"
	LimitTypeIp       = "1"
	GiveOutTypeEmail  = "0"
	GiveOutTypeRedeem = "1"
	GiveOutTypePush   = "2" // 推送平台
	// 仅一次
	AnswerLimitTypeOnlyOne = 1
	// 限定次数
	AnswerLimitTypeLimit = 2
	// 无限制
	AnswerLimitTypeUnlimited = 3

	LoginRoleid = 0
	LoginOpenid = 1
	LoginDevice = 2
	LoginIp     = 3
)

var (
	cmsSurveys     = memory.NewTTLCache()
	cmsSurveyGroup = singleflight.Group{}
)

func GetCmsSurvey(surveyId string) (*proto.CmsSurveyVersion, error) {
	cmsSurvey := cmsSurveys.Get(surveyId)
	if cmsSurvey != nil {
		cs, ok := cmsSurvey.(*proto.CmsSurveyVersion)
		if ok {
			return cs, nil
		}
	}
	return ReloadCmsSurvey(surveyId)
}

func ReloadCmsSurvey(surveyId string) (*proto.CmsSurveyVersion, error) {
	value, _ := cmsSurveyGroup.Do(surveyId, func() (interface{}, error) {
		cs, err := getCmsSurveyRaw(surveyId)
		if err != nil {
			return nil, err
		}
		ttl := config.Get().CmsSurveyTTL
		if ttl == 0 {
			ttl = 5 * time.Minute
		}
		cmsSurveys.Set(surveyId, cs, ttl, nil)
		return cs, nil
	})
	if value.Err != nil {
		return nil, value.Err
	}
	cs, ok := value.Val.(*proto.CmsSurveyVersion)

	if !ok {
		return nil, errors.New("cms survey error")
	}

	return cs, nil
}

// getDB
func getCmsSurveyRaw(surveryId string) (*proto.CmsSurveyVersion, error) {
	db := database.Get()
	var cs *proto.CmsSurveyVersion
	err := db.Where("hash_code = ?", surveryId).Order("version_id desc").First(&cs).Error
	if err != nil {
		return nil, err
	}

	setting := cs.GetSettings()
	if setting != "" {
		cs.SettingStruct = new(proto.CmsSurveyVersion_Setting)
		err := json.Unmarshal([]byte(setting), cs.SettingStruct)
		if err != nil {
			return nil, err
		}
	}

	value := gjson.Get(cs.GetWebSettings(), "languageList")
	if value.IsArray() {
		cs.DefaultLanguage = value.Array()[0].String()
	}

	r, err := gzip.NewReader(bytes.NewReader(cs.MultilingualSchema))
	if err == nil {
		multilingualSchema, err := io.ReadAll(r)
		if err == nil {
			var m map[string]interface{}
			err = json.Unmarshal(multilingualSchema, &m)
			if err != nil {
				return nil, err
			}
			cs.MultilingualSchemaDecMap = make(map[string][]byte)
			for k := range m {
				cs.MultilingualSchemaDecMap[k] = []byte(gjson.Get(string(multilingualSchema), k).String())
			}
			return cs, nil
		}
	}

	// 多语言schema解压失败，尝试解压单语言schema，作老数据兼容
	r, err = gzip.NewReader(bytes.NewReader(cs.Schema))
	if err != nil {
		return nil, err
	}

	schemaDec, err := io.ReadAll(r)
	if err != nil {
		return nil, err
	}

	cs.SchemaDec = schemaDec

	cs.KeyValueMap = make(map[string]string)
	if len(cs.KeyValue) > 0 {
		var tempMap map[string]string
		err := json.Unmarshal([]byte(cs.KeyValue), &tempMap)
		if err != nil {
			xlog.Error("cms survey keyvalue is invalid", xlog.String("survey", cs.HashCode))
			shared.MetricsErrorOccurred.Add(1, "config_error", "keyvalue_invalid", cs.HashCode)
		} else {
			for k, v := range tempMap {
				key := shared.GetLastNChars(k, 10)
				cs.KeyValueMap[strings.TrimSpace(key)] = v
			}
		}
	}
	return cs, nil
}

func CmsSurveyInEffect(ctx context.Context, cs *proto.CmsSurveyVersion, timezone string) error {
	logger := xlog.FromContext(ctx)

	setting := cs.GetSettingStruct()
	if setting == nil {
		logger.Error(fmt.Sprintf("cms survey(%v) setting is nil", cs.HashCode))
		return shared.EcodeSurveyConfigInvalid
	}

	if cs.GetIsDelete() != 0 {
		logger.Error(fmt.Sprintf("cms survey(%v) is deleted", cs.HashCode))
		return shared.EcodeSurveyDeleted
	}

	if cs.GetIsPause() != 0 {
		logger.Error(fmt.Sprintf("cms survey(%v) is paused", cs.HashCode))
		return shared.EcodeSurveyPaused
	}

	now := time.Now()

	var tz int
	var err error
	if timezone != "" {
		tz, err = strconv.Atoi(timezone)
		if err != nil {
			logger.Error(fmt.Sprintf("cms survey(%v) timezone(%v) is invalid", cs.HashCode, timezone))
			return ecode.BadRequest
		}
	}

	stime := cs.GetStime()
	if stime != nil {
		t := stime.AsTime()
		// 时刻偏移, 时区偏移
		if timezone != "" && t.Unix() != 0 {
			t, err = OffsetTimeByTimezone(cs.Clientid, int32(tz), t)
			if err != nil {
				logger.Error(fmt.Sprintf("cms survey(%v) OffsetTimeByTimezone error(%v)", cs.HashCode, err))
				return shared.EcodeSurveyConfigInvalid
			}
		}
		if now.Before(t) {
			logger.Error(fmt.Sprintf("cms survey(%v) not started", cs.HashCode))
			return shared.EcodeSurveyNotStarted
		}
	}

	etime := cs.GetEtime()
	if etime != nil {
		t := etime.AsTime()
		// 时刻偏移,时区偏移
		if timezone != "" && t.Unix() != 0 {
			t, err = OffsetTimeByTimezone(cs.Clientid, int32(tz), t)
			if err != nil {
				logger.Error(fmt.Sprintf("cms survey(%v) OffsetTimeByTimezone error(%v)", cs.HashCode, err))
				return shared.EcodeSurveyConfigInvalid
			}
		}
		if t.Unix() != 0 && now.After(t) {
			logger.Error(fmt.Sprintf("cms survey(%v) over", cs.HashCode))
			return shared.EcodeSurveyOver
		}
	}
	return nil
}

func GetCmsSurveyType(ctx context.Context, cs *proto.CmsSurveyVersion) (int, error) {
	logger := xlog.FromContext(ctx)

	setting := cs.GetSettingStruct()
	if setting == nil {
		logger.Error(fmt.Sprintf("cms survey(%v) setting is nil", cs.HashCode))
		return 0, shared.EcodeSurveyConfigInvalid
	}

	brcfg := setting.GetBaseRuleConfig()
	if brcfg == nil {
		logger.Error(fmt.Sprintf("cms survey(%v) br cfg is nil", cs.HashCode))
		return 0, shared.EcodeSurveyConfigInvalid
	}

	loginType := brcfg.GetLoginType()
	switch loginType {
	case LoginTypeRoleid:
		return SurveyTypeRoleId, nil
	case LoginTypeOpenid:
		return SurveyTypeOpenid, nil
	case LoginTypeNo:
		limitcfg := setting.GetAnswerLimitConfig()
		if limitcfg == nil {
			logger.Error(fmt.Sprintf("cms survey(%v) limit cfg is nil", cs.HashCode))
			return 0, shared.EcodeSurveyConfigInvalid
		}

		limitType := limitcfg.GetLimitType()
		switch limitType {
		case LimitTypeDevice:
			return SurveyTypeDeviceId, nil
		case LimitTypeIp:
			return SurveyTypeIp, nil
		default:
			return SurveyTypeAny, nil
		}
	default:
		logger.Error(fmt.Sprintf("cms survey(%v) login type(%v) is invalid", cs.HashCode, loginType))
		return 0, shared.EcodeSurveyConfigInvalid
	}
}

// 答题次数
func GetCmsSurveyAnswerTimes(ctx context.Context, cs *proto.CmsSurveyVersion) (int32, error) {
	logger := xlog.FromContext(ctx)

	setting := cs.GetSettingStruct()
	if setting == nil {
		logger.Error(fmt.Sprintf("cms survey(%v) setting is nil", cs.HashCode))
		return 0, shared.EcodeSurveyConfigInvalid
	}

	brcfg := setting.GetBaseRuleConfig()
	if brcfg == nil {
		logger.Error(fmt.Sprintf("cms survey(%v) br cfg is nil", cs.HashCode))
		return 0, shared.EcodeSurveyConfigInvalid
	}

	atcfg := brcfg.GetAnswerTimesConfig()
	if atcfg == nil {
		logger.Error(fmt.Sprintf("cms survey(%v) at cfg is nil", cs.HashCode))
		return 0, shared.EcodeSurveyConfigInvalid
	}

	switch atcfg.GetLimitType() {
	case AnswerLimitTypeOnlyOne:
		return 1, nil
	case AnswerLimitTypeLimit:
		if atcfg.GetTimes() < 0 {
			logger.Error(fmt.Sprintf("cms survey(%v) at cfg times is invalid", cs.HashCode))
			return 0, shared.EcodeSurveyConfigInvalid
		}
		return atcfg.GetTimes(), nil
	case AnswerLimitTypeUnlimited:
		return -1, nil
	default:
		logger.Error(fmt.Sprintf("cms survey(%v) answer limit type(%v) is invalid", cs.HashCode, atcfg.GetLimitType()))
		return 0, shared.EcodeSurveyConfigInvalid
	}
}

func GetCmsSurveyZoneIds(ctx context.Context, cs *proto.CmsSurveyVersion) ([]int32, error) {
	logger := xlog.FromContext(ctx)

	setting := cs.GetSettingStruct()
	if setting == nil {
		logger.Error(fmt.Sprintf("cms survey(%v) setting is nil", cs.HashCode))
		return nil, shared.EcodeSurveyConfigInvalid
	}

	zicfg := setting.GetZoneIds()
	if len(zicfg) == 0 {
		return []int32{}, nil
	}

	return zicfg, nil
}

func OffsetTimeByTimezone(clientid string, timezone int32, t time.Time) (time.Time, error) {
	cltCfg, err := GetClientConfig(clientid)
	if err != nil {
		return time.Time{}, err
	}
	return t.Add(time.Duration(cltCfg.DefaultTimezone-timezone) * time.Hour), nil
}
