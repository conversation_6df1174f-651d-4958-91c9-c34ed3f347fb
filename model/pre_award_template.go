package model

import (
	"errors"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/cache/memory"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/singleflight"
	"gitlab.papegames.com/fringe/survey/config"
	"gitlab.papegames.com/fringe/survey/database"
	"gitlab.papegames.com/fringe/survey/proto"
)

var (
	preAwardTemplates     = memory.NewTTLCache()
	preAwardTemplateGroup = singleflight.Group{}
)

func GetPreAwardTemplate(templateid string) (*proto.PreAwardTemplate, error) {
	preAwardTemplate := preAwardTemplates.Get(templateid)
	if preAwardTemplate != nil {
		pat, ok := preAwardTemplate.(*proto.PreAwardTemplate)
		if ok {
			return pat, nil
		}
	}
	value, _ := preAwardTemplateGroup.Do(templateid, func() (interface{}, error) {
		pat, err := getPreAwardTemplateRaw(templateid)
		if err != nil {
			return nil, err
		}
		ttl := config.Get().PreAwardTemplateTTL
		if ttl == 0 {
			ttl = 5 * time.Minute
		}
		preAwardTemplates.Set(templateid, pat, ttl, nil)
		return pat, nil
	})
	if value.Err != nil {
		return nil, value.Err
	}
	pat, ok := value.Val.(*proto.PreAwardTemplate)
	if !ok {
		return nil, errors.New("pre award template type error")
	}
	return pat, nil
}

func getPreAwardTemplateRaw(templateid string) (*proto.PreAwardTemplate, error) {
	db := database.GetNaward()
	var pat = new(proto.PreAwardTemplate)
	err := db.Where("id = ?", templateid).First(pat).Error
	return pat, err
}
