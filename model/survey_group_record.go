package model

import (
	"fmt"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gitlab.papegames.com/fringe/survey/proto"
)

func SurveyGroupRecordTable(groupId string) string {
	return "survey_group_record_" + groupId
}

func CreateSurveyGroupRecord(d *xgorm.DB,
	groupId string,
	record *proto.SurveyGroupRecord) (int32, error) {
	record.Ctime = xtype.NewTimestamp(time.Now())
	err := d.Table(SurveyGroupRecordTable(groupId)).Save(record).Error
	if err != nil {
		return 0, err
	}
	return record.Id, nil
}

// 答题次数是否超过限制
func SurveyGroupRecordOverflowed(d *xgorm.DB, csg *proto.CmsSurveyGroupVersion, ss *SurveySelector) (bool, error) {
	if csg.LimitType == proto.CmsSurveyGroupVersion_Unlimited {
		return false, nil
	}
	var count int64
	var err error
	tx := d.Table(SurveyGroupRecordTable(csg.HashCode))
	// 根据不同的答题类型，查询答题记录, 无效的也算
	switch csg.LimitType {
	case proto.CmsSurveyGroupVersion_OpenIdOnce:
		err = tx.Where("openid = ?", ss.Openid).Count(&count).Error
	case proto.CmsSurveyGroupVersion_RoleIdOnce:
		err = tx.Where("role_id = ?", ss.RoleId).Count(&count).Error
	default:
		err = fmt.Errorf("unknown survey group type (%v,%v)", csg.HashCode, csg.LimitType)
	}
	if err != nil {
		return false, err
	}
	return count >= 1, nil
}
