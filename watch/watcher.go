package watch

import (
	"fmt"

	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/watch"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/pattern/watch/provider"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/survey/model"
)

var (
	watcher *watch.Watcher
)

const (
	surveyConfig = "/sparrow/watch/survey/config"
	surveyGroup  = "/sparrow/watch/surveygroup/config"
)

func Startup() error {
	var err error
	watcher, err = watch.StdConfig().Build()
	if err != nil {
		xlog.Error(fmt.Sprintf("watcher startup failed: %v", err))
		return err
	}
	_ = watcher.Watch(surveyConfig, func(res *watch.Response) error {
		_, err := model.ReloadCmsSurvey(res.Value)
		if err != nil {
			xlog.Error(fmt.Sprintf("watch: ReloadCmsSurvey(%v) with error(%v)", res.Value, err))
			return err
		}
		xlog.Info(fmt.Sprintf("watch: ReloadCmsSurvey(%v) success", res.Value))
		return nil
	})
	_ = watcher.Watch(surveyGroup, func(res *watch.Response) error {
		_, err := model.ReloadCmsSurveyGroup(res.Value)
		if err != nil {
			xlog.Error(fmt.Sprintf("watch: ReloadCmsSurveyGroup(%v) with error(%v)", res.Value, err))
			return err
		}
		xlog.Info(fmt.Sprintf("watch: ReloadCmsSurveyGroup(%v) success", res.Value))
		return nil
	})
	return nil
}

func ExpireSurveyConfig(surveyId string) error {
	return watcher.Expire(surveyConfig, surveyId)
}

func ExpireSurveyGroupConfig(groupId string) error {
	return watcher.Expire(surveyGroup, groupId)
}
